<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="Content-Security-Policy" content="default-src 'self'; script-src 'self' 'unsafe-inline'; style-src 'self' 'unsafe-inline'; connect-src 'self' https://secure-notes-api.v8x.workers.dev; img-src 'self' data:; font-src 'self'">
    <meta http-equiv="Strict-Transport-Security" content="max-age=31536000; includeSubDomains">
    <meta http-equiv="X-Content-Type-Options" content="nosniff">
    <meta http-equiv="Referrer-Policy" content="strict-origin-when-cross-origin">
    <title>SecureNotes - My Notes</title>
    <link rel="stylesheet" href="styles/common.css">
    <link rel="stylesheet" href="styles/notes.css">
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔐 SecureNotes</h1>
            <p>Your secure, encrypted notes</p>
        </div>

        <!-- Navigation -->
        <div class="nav-tabs">
            <button class="nav-tab active">My Notes</button>
            <a href="settings.html" class="nav-tab">Settings</a>
            <a href="admin.html" class="nav-tab" id="admin-nav" style="display: none;">Admin</a>
            <button class="nav-tab" onclick="logout()">Logout</button>
        </div>

        <!-- Notes Status -->
        <div id="notes-status" class="status"></div>

        <!-- Note Input Section -->
        <div class="form-group">
            <label for="note-content">Write Note</label>
            <textarea id="note-content" placeholder="Write your thoughts here..." maxlength="1000"></textarea>
            <div class="char-counter" id="note-char-counter">0/1000</div>
        </div>

        <!-- Note Controls -->
        <div class="note-controls">
            <button class="btn" id="save-note-btn">Save Note</button>
            <button class="btn btn-danger" id="clear-all-notes-btn">Clear All Notes</button>
            
            <!-- Search controls -->
            <div class="search-section">
                <div class="search-controls">
                    <select id="search-scope" class="search-scope-select" title="Search scope">
                        <option value="title">Title only (Fast)</option>
                        <option value="full">Full content (Slower)</option>
                    </select>
                    <div class="search-container">
                        <input type="text" id="note-search" placeholder="Search notes..." />
                        <button class="search-clear-btn" id="search-clear-btn" title="Clear search">×</button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Notes List -->
        <div id="notes-list">
            <h4>Loading notes...</h4>
        </div>

        <!-- Pagination Controls -->
        <div id="pagination-controls" class="pagination-controls" style="display: none;">
            <button class="pagination-btn" id="prev-page" onclick="changePage(-1)">Previous</button>
            <span class="pagination-info" id="pagination-info">Page 1 of 1</span>
            <button class="pagination-btn" id="next-page" onclick="changePage(1)">Next</button>
        </div>

        <!-- User Stats -->
        <div id="user-stats" style="text-align: center; margin-top: 20px; color: #6c757d; font-size: 14px;">
            <p>Loading statistics...</p>
        </div>
    </div>

    <!-- Loading indicators -->
    <div id="load-notes" class="loading">
        <p>Loading notes...</p>
    </div>

    <div id="load-save" class="loading">
        <p>Saving note...</p>
    </div>

    <div id="load-delete" class="loading">
        <p>Deleting note...</p>
    </div>

    <!-- Scripts -->
    <script src="js/utils.js"></script>
    <script src="js/crypto.js"></script>
    <script src="js/api.js"></script>
    <script src="js/notes.js"></script>
    <script>
        // Initialize the notes page
        document.addEventListener('DOMContentLoaded', function() {
            // Initialize API manager first
            if (typeof initializeAPI === 'function') {
                initializeAPI();
            }

            verifyConfigIntegrity();
            initializeActivityTracking();
            initializeNotesPage();
        });

        // Check authentication on page load
        function checkAuth() {
            if (!currentUser || !encryptionKey || !apiManager || !apiManager.isTokenValid()) {
                window.location.href = 'index.html';
                return false;
            }
            return true;
        }

        // Initialize notes page
        function initializeNotesPage() {
            if (!checkAuth()) return;
            
            loadNotes();
            loadUserStats();
            checkAdminStatus();
            
            // Set up event listeners
            setupEventListeners();
        }

        function setupEventListeners() {
            // Save note button
            document.getElementById('save-note-btn').addEventListener('click', saveNote);
            
            // Clear all notes button
            document.getElementById('clear-all-notes-btn').addEventListener('click', function() {
                if (confirm('Are you sure you want to delete ALL notes? This action cannot be undone.')) {
                    clearAllNotes();
                }
            });

            // Character counter for note content
            document.getElementById('note-content').addEventListener('input', function(e) {
                updateCharCounter(e.target, 'note-char-counter', 1000);
                
                const length = e.target.value.length;
                const maxLength = CONFIG.MAX_NOTE_LENGTH;
                if (length > maxLength * 0.9) {
                    showStatus('notes-status', `Character count: ${length}/${maxLength}`, 'info');
                }
            });

            // Ctrl+S shortcut for saving
            document.getElementById('note-content').addEventListener('keydown', function(e) {
                if (e.key === 's' && (e.ctrlKey || e.metaKey)) {
                    e.preventDefault();
                    saveNote();
                }
            });

            // Search functionality
            setupSearchListeners();
        }

        function setupSearchListeners() {
            const searchInput = document.getElementById('note-search');
            const searchClearBtn = document.getElementById('search-clear-btn');
            const searchScope = document.getElementById('search-scope');
            let searchTimeout;
            
            // Search input with debouncing
            searchInput.addEventListener('input', function(e) {
                const keyword = e.target.value.trim();
                searchClearBtn.style.display = keyword ? 'flex' : 'none';
                
                clearTimeout(searchTimeout);
                
                if (keyword.length >= 1 || keyword.length === 0) {
                    const debounceDelay = searchScope.value === 'title' ? 200 : 400;
                    searchTimeout = setTimeout(() => {
                        searchNotes(keyword);
                    }, debounceDelay);
                }
            });

            // Search scope change
            searchScope.addEventListener('change', function() {
                const keyword = searchInput.value.trim();
                if (keyword.length >= 1) {
                    clearTimeout(searchTimeout);
                    searchNotes(keyword);
                }
            });

            // Clear search
            searchClearBtn.addEventListener('click', function() {
                searchInput.value = '';
                searchClearBtn.style.display = 'none';
                clearTimeout(searchTimeout);
                searchNotes('');
                searchInput.focus();
            });
        }

        // Check admin status and show admin nav if needed
        async function checkAdminStatus() {
            try {
                const result = await apiManager.checkAdminStatus();
                if (result.isAdmin) {
                    document.getElementById('admin-nav').style.display = 'block';
                }
            } catch (error) {
                // Not admin or error checking
            }
        }

        // Page navigation functions
        function changePage(direction) {
            const newPage = currentPage + direction;
            if (newPage >= 1 && newPage <= totalPages) {
                loadNotes(newPage);
            }
        }

        function updatePaginationControls() {
            const paginationControls = document.getElementById('pagination-controls');
            const prevBtn = document.getElementById('prev-page');
            const nextBtn = document.getElementById('next-page');
            const pageInfo = document.getElementById('pagination-info');

            if (totalPages <= 1) {
                paginationControls.style.display = 'none';
                return;
            }

            paginationControls.style.display = 'flex';
            prevBtn.disabled = currentPage <= 1;
            nextBtn.disabled = currentPage >= totalPages;
            pageInfo.textContent = `Page ${currentPage} of ${totalPages}`;
        }

        // Expand/collapse note functions
        function expandNote(noteId) {
            // This function will be implemented in notes.js
            if (typeof expandNoteContent === 'function') {
                expandNoteContent(noteId);
            }
        }

        function collapseNote(noteId) {
            // This function will be implemented in notes.js
            if (typeof collapseNoteContent === 'function') {
                collapseNoteContent(noteId);
            }
        }
    </script>
</body>
</html>
