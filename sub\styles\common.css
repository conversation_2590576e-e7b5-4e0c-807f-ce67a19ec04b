/* Common styles for SecureNotes application - Extracted from index_old.html */

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Robot<PERSON>, sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #333;
}

.access-gate {
    background: white;
    border-radius: 16px;
    padding: 40px;
    box-shadow: 0 20px 60px rgba(0,0,0,0.2);
    backdrop-filter: blur(20px);
    max-width: 400px;
    width: 90%;
    text-align: center;
}

.lock-icon {
    font-size: 4rem;
    margin-bottom: 20px;
    color: #667eea;
}

h1 {
    color: #333;
    margin-bottom: 10px;
    font-size: 1.8rem;
}

.subtitle {
    color: #666;
    margin-bottom: 30px;
    font-size: 0.95rem;
}

.form-group {
    margin-bottom: 20px;
    text-align: left;
}

label {
    display: block;
    margin-bottom: 8px;
    font-weight: 600;
    color: #555;
}

input[type="password"], input[type="text"], input[type="email"], textarea {
    width: 100%;
    padding: 14px;
    border: 2px solid #e1e5e9;
    border-radius: 8px;
    font-size: 16px;
    transition: border-color 0.3s;
}

/* Password input container with eye icon */
.password-container {
    position: relative;
    display: flex;
    align-items: center;
}

.password-container input[type="password"],
.password-container input[type="text"] {
    padding-right: 45px; /* Make room for eye icon */
}

.password-toggle {
    position: absolute;
    right: 12px;
    top: 50%;
    transform: translateY(-50%);
    cursor: pointer;
    font-size: 18px;
    color: #666;
    user-select: none;
    transition: color 0.3s;
}

.password-toggle:hover {
    color: #333;
}

/* Password strength indicator */
.password-hint {
    margin-top: 8px;
    padding: 12px;
    background: #f8f9fa;
    border-left: 4px solid #17a2b8;
    border-radius: 4px;
    font-size: 14px;
    line-height: 1.4;
}

.password-hint.warning {
    background: #fff3cd;
    border-left-color: #ffc107;
    color: #856404;
}

.password-hint.danger {
    background: #f8d7da;
    border-left-color: #dc3545;
    color: #721c24;
}

.password-hint.success {
    background: #d4edda;
    border-left-color: #28a745;
    color: #155724;
}

.password-requirements {
    margin-top: 5px;
    font-size: 13px;
}

.password-requirements ul {
    margin: 5px 0;
    padding-left: 20px;
}

.password-requirements li {
    margin: 2px 0;
}

.requirement-met {
    color: #28a745;
}

.requirement-unmet {
    color: #dc3545;
}

input:focus, textarea:focus {
    outline: none;
    border-color: #667eea;
}

textarea {
    resize: vertical;
    min-height: 120px;
    font-family: inherit;
}

.btn {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border: none;
    padding: 14px 32px;
    border-radius: 8px;
    cursor: pointer;
    font-size: 16px;
    font-weight: 600;
    transition: transform 0.2s, box-shadow 0.2s;
    width: 100%;
    margin-bottom: 10px;
}

.btn:hover:not(:disabled) {
    transform: translateY(-2px);
    box-shadow: 0 8px 20px rgba(102, 126, 234, 0.4);
}

.btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none;
}

/* Loading spinner styles */
.btn.loading {
    position: relative;
    color: transparent;
    pointer-events: none;
}

.btn.loading::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 20px;
    height: 20px;
    border: 2px solid rgba(255, 255, 255, 0.3);
    border-top: 2px solid white;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: translate(-50%, -50%) rotate(0deg); }
    100% { transform: translate(-50%, -50%) rotate(360deg); }
}

/* Icon Button Styles */
.btn-icon {
    width: auto;
    min-width: 44px;
    height: 44px;
    padding: 8px 12px;
    margin-bottom: 0;
    margin-left: 8px;
    font-size: 18px;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    border-radius: 8px;
    position: relative;
}

.btn-icon:hover {
    transform: translateY(-1px) scale(1.05);
    box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
}

.btn-icon:first-child {
    margin-left: 0;
}

/* Tooltip styles for icon buttons */
.btn-icon[title]:hover::after {
    content: attr(title);
    position: absolute;
    bottom: -35px;
    left: 50%;
    transform: translateX(-50%);
    background: rgba(0, 0, 0, 0.8);
    color: white;
    padding: 6px 10px;
    border-radius: 4px;
    font-size: 12px;
    font-weight: normal;
    white-space: nowrap;
    z-index: 1000;
    pointer-events: none;
}

.btn-icon[title]:hover::before {
    content: '';
    position: absolute;
    bottom: -8px;
    left: 50%;
    transform: translateX(-50%);
    border: 4px solid transparent;
    border-bottom-color: rgba(0, 0, 0, 0.8);
    z-index: 1001;
    pointer-events: none;
}

/* Admin Styles */
.btn-admin {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border: none;
}

.btn-admin:hover {
    background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
}

.btn-admin.btn-icon:hover {
    background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
    transform: translateY(-1px) scale(1.05);
    box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
}

.admin-section {
    display: none;
}

.admin-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 30px;
    padding-bottom: 15px;
    border-bottom: 2px solid #e9ecef;
}

.admin-panel {
    margin: 25px 0;
    padding: 20px;
    border: 1px solid #dee2e6;
    border-radius: 8px;
    background: #f8f9fa;
}

.admin-panel h4 {
    margin-bottom: 15px;
    color: #495057;
    font-weight: 600;
}

.backup-controls, .user-controls {
    margin: 15px 0;
    display: flex;
    gap: 10px;
    flex-wrap: wrap;
}

.backup-controls .btn {
    flex: 1;
    min-width: 140px;
}

.status-display {
    margin-top: 15px;
    padding: 15px;
    border-radius: 6px;
    background: white;
    border: 1px solid #e9ecef;
    min-height: 50px;
}

.users-list {
    margin-top: 15px;
    max-height: 400px;
    overflow-y: auto;
}

.user-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px;
    border: 1px solid #e9ecef;
    margin: 8px 0;
    border-radius: 6px;
    background: white;
    transition: all 0.2s ease;
}

.user-item:hover {
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.user-disabled {
    background-color: #f8d7da;
    border-color: #f5c6cb;
    opacity: 0.8;
}

.user-info {
    flex: 1;
}

.user-email {
    font-weight: 600;
    color: #495057;
}

.user-meta {
    font-size: 12px;
    color: #6c757d;
    margin-top: 4px;
}

.user-actions {
    display: flex;
    gap: 8px;
    align-items: center;
}

.toggle-user-btn {
    padding: 6px 12px;
    font-size: 12px;
    border-radius: 4px;
    border: none;
    cursor: pointer;
    transition: all 0.2s ease;
    position: relative;
}

.toggle-user-btn.enable {
    background: #28a745;
    color: white;
}

.toggle-user-btn.disable {
    background: #dc3545;
    color: white;
}

.toggle-user-btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0,0,0,0.2);
}

.toggle-user-btn:disabled {
    background: #6c757d !important;
    color: #fff !important;
    cursor: not-allowed !important;
    opacity: 0.6;
    transform: none !important;
    box-shadow: none !important;
}

.toggle-user-btn:disabled:hover {
    transform: none !important;
    box-shadow: none !important;
}

/* Loading state for toggle-user-btn */
.toggle-user-btn.loading {
    color: transparent !important;
    pointer-events: none;
}

.toggle-user-btn.loading::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 16px;
    height: 16px;
    border: 2px solid rgba(255, 255, 255, 0.3);
    border-top: 2px solid white;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

.user-status {
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 11px;
    font-weight: 600;
    text-transform: uppercase;
}

.user-status.active {
    background: #d4edda;
    color: #155724;
}

.user-status.disabled {
    background: #f8d7da;
    color: #721c24;
}

.admin-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 15px;
    margin: 15px 0;
}

.stat-card {
    padding: 15px;
    background: white;
    border: 1px solid #e9ecef;
    border-radius: 6px;
    text-align: center;
}

.stat-number {
    font-size: 24px;
    font-weight: bold;
    color: #495057;
}

.stat-label {
    font-size: 12px;
    color: #6c757d;
    text-transform: uppercase;
}

/* Progress bar styles */
.progress-bar {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 3px;
    background: rgba(102, 126, 234, 0.2);
    z-index: 9999;
    display: none;
}

.progress-bar.active {
    display: block;
}

.progress-bar::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    height: 100%;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    width: 0%;
    animation: progress 2s ease-in-out infinite;
}

@keyframes progress {
    0% { width: 0%; }
    50% { width: 70%; }
    100% { width: 100%; }
}

/* Main application styles */
.main-app {
    display: none;
    width: 100%;
    min-height: 100vh;
}

.main-app.show {
    display: block;
}

.container {
    max-width: 800px;
    margin: 0 auto;
    padding: 20px;
    min-height: 100vh;
}

.header {
    text-align: center;
    margin-bottom: 30px;
    color: white;
}

.header h1 {
    font-size: 2.5rem;
    margin-bottom: 10px;
    text-shadow: 0 2px 4px rgba(0,0,0,0.3);
    color: white;
}

.header p {
    opacity: 0.9;
    font-size: 1.1rem;
}

.card {
    background: white;
    border-radius: 12px;
    padding: 24px;
    margin-bottom: 20px;
    box-shadow: 0 8px 32px rgba(0,0,0,0.1);
    backdrop-filter: blur(10px);
}

.auth-section {
    margin-bottom: 20px;
}

.auth-form {
    display: block;
}

.auth-form.hidden {
    display: none;
}

.auth-toggle {
    text-align: center;
    margin-top: 15px;
}

.auth-toggle a {
    color: #667eea;
    text-decoration: none;
    font-weight: 500;
    cursor: pointer;
}

.auth-toggle a:hover {
    text-decoration: underline;
}

.auth-form h3 {
    color: #333;
    margin-bottom: 20px;
    font-size: 1.3rem;
}

.status {
    padding: 12px;
    border-radius: 8px;
    margin-bottom: 16px;
    font-weight: 500;
}

.status.success {
    background: #d4edda;
    color: #155724;
    border: 1px solid #c3e6cb;
}

.status.error {
    background: #f8d7da;
    color: #721c24;
    border: 1px solid #f5c6cb;
}

.status.info {
    background: #d1ecf1;
    color: #0c5460;
    border: 1px solid #bee5eb;
}

.notes-section {
    display: none;
}

.notes-section.active {
    display: block;
}

.notes-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    flex-wrap: wrap;
    gap: 10px;
}

.notes-header h3 {
    color: #333;
    font-size: 1.5rem;
}

.header-buttons {
    display: flex;
    align-items: center;
    gap: 8px;
}

.settings-section {
    display: none;
}

.settings-section.active {
    display: block;
}

.settings-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    border-bottom: 2px solid #e0e0e0;
    padding-bottom: 15px;
}

.settings-group {
    margin-bottom: 30px;
    padding: 20px;
    border: 1px solid #e0e0e0;
    border-radius: 8px;
    background-color: #fafafa;
}

.settings-group h4 {
    margin: 0 0 10px 0;
    color: #333;
    font-size: 1.1em;
}

.settings-description {
    margin-bottom: 20px;
    color: #666;
    font-size: 0.9em;
    line-height: 1.4;
}

.info-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 10px;
    padding: 8px 0;
}

.info-item label {
    font-weight: bold;
    color: #555;
}

.info-item span {
    color: #333;
}

.note-controls {
    display: flex;
    gap: 10px;
    justify-content: space-between;
    align-items: center;
    margin: 20px 0;
    flex-wrap: wrap;
}

.note-item {
    background: #f8f9fa;
    border-radius: 8px;
    padding: 16px;
    margin-bottom: 12px;
    border-left: 4px solid #667eea;
    position: relative;
}

.note-item.edit-mode {
    background: #fff3cd;
    border-left-color: #ffc107;
    padding: 12px 16px;
}

.note-item.edit-mode .note-actions {
    margin-bottom: 8px;
}

.edit-textarea {
    width: 100%;
    min-height: 150px;
    max-height: 300px;
    padding: 12px;
    border: 2px solid #ffc107;
    border-radius: 6px;
    font-family: inherit;
    font-size: 14px;
    line-height: 1.5;
    resize: vertical;
    margin-top: 8px;
    margin-right: 0;
    box-sizing: border-box;
}

.edit-actions {
    display: flex;
    gap: 10px;
    margin-top: 8px;
    justify-content: flex-end;
    align-items: center;
}

.note-actions {
    position: absolute;
    top: 12px;
    right: 12px;
    display: flex;
    gap: 5px;
}

.note-meta {
    font-size: 12px;
    color: #6c757d;
    margin-bottom: 8px;
}

.note-content {
    line-height: 1.6;
    white-space: pre-wrap;
    word-break: break-word;
    margin-right: 80px;
}

.note-item.edit-mode .note-content {
    margin-right: 0;
}

.note-content.collapsed {
    display: -webkit-box;
    -webkit-line-clamp: 1;
    -webkit-box-orient: vertical;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.note-expand-btn {
    background: none;
    border: none;
    color: #667eea;
    cursor: pointer;
    font-size: 12px;
    margin-top: 8px;
    padding: 4px 8px;
    text-decoration: underline;
    align-self: flex-start;
}

.note-expand-btn:hover {
    color: #5a6fd8;
}

.char-counter {
    position: absolute;
    bottom: 8px;
    right: 8px;
    font-size: 12px;
    color: #666;
    background: rgba(255, 255, 255, 0.9);
    padding: 2px 6px;
    border-radius: 3px;
    pointer-events: none;
}

.edit-char-counter {
    font-size: 12px;
    color: #666;
    margin-right: auto;
    padding: 2px 6px;
}

.edit-char-counter.warning {
    color: #ff6b35;
}

.edit-char-counter.error {
    color: #dc3545;
}

.char-counter.warning {
    color: #ff6b35;
}

.char-counter.error {
    color: #dc3545;
}

.form-group {
    position: relative;
}

.search-container {
    position: relative;
    display: inline-block;
    margin-left: 15px;
}

.search-container input {
    width: 200px;
    padding: 8px 30px 8px 12px;
    border: 2px solid #e1e5e9;
    border-radius: 6px;
    font-size: 14px;
    transition: border-color 0.3s;
}

.search-container input:focus {
    outline: none;
    border-color: #667eea;
}

.search-clear-btn {
    position: absolute;
    right: 8px;
    top: 50%;
    transform: translateY(-50%);
    background: none;
    border: none;
    font-size: 18px;
    color: #999;
    cursor: pointer;
    padding: 0;
    width: 20px;
    height: 20px;
    display: none;
    align-items: center;
    justify-content: center;
}

.search-clear-btn:hover {
    color: #666;
}

.search-container input:not(:placeholder-shown) + .search-clear-btn {
    display: flex;
}

.search-section {
    display: flex;
    align-items: center;
    margin-left: 15px;
}

.search-controls {
    display: flex;
    align-items: center;
    gap: 8px;
}

.search-scope-select {
    padding: 6px 8px;
    border: 2px solid #e1e5e9;
    border-radius: 6px;
    font-size: 12px;
    background: white;
    cursor: pointer;
    min-width: 90px;
}

.search-scope-select:focus {
    outline: none;
    border-color: #667eea;
}

.crypto-info {
    background: #e7f3ff;
    border: 1px solid #b3d9ff;
    border-radius: 8px;
    padding: 16px;
    margin-bottom: 20px;
}

.crypto-info h3 {
    color: #0066cc;
    margin-bottom: 8px;
}

.crypto-info p {
    font-size: 14px;
    color: #0066cc;
    margin-bottom: 4px;
}

.edit-mode {
    background: #fff3cd;
    border-left-color: #ffc107;
}

.edit-textarea {
    width: 100%;
    padding: 8px;
    border: 1px solid #ddd;
    border-radius: 4px;
    resize: vertical;
    min-height: 80px;
    font-family: inherit;
    margin-right: 0;
}

.loading {
    text-align: center;
    padding: 20px;
    color: #6c757d;
}

/* Responsive design */
@media (max-width: 768px) {
    .auth-section {
        flex-direction: column;
    }

    .header h1 {
        font-size: 2rem;
    }

    .note-content {
        margin-right: 0;
        margin-bottom: 40px;
    }

    .note-actions {
        position: static;
        margin-top: 10px;
        justify-content: flex-end;
    }

    .note-controls {
        flex-direction: column;
        gap: 10px;
    }

    .search-section {
        margin-left: 0;
        align-self: stretch;
        flex-direction: column;
        align-items: stretch;
    }

    .search-controls {
        flex-direction: column;
        gap: 8px;
    }

    .search-container {
        margin-left: 0;
    }

    .search-container input {
        width: 100%;
    }

    .search-scope-select {
        width: 100%;
        min-width: auto;
    }

    .notes-header {
        flex-direction: column;
        align-items: stretch;
    }

    .note-controls {
        flex-direction: column;
        gap: 15px;
    }
}
