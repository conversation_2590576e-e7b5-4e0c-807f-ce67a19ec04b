<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Automated Login Test</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ccc; }
        .error { color: red; }
        .success { color: green; }
        .info { color: blue; }
        .warning { color: orange; }
        button { margin: 5px; padding: 10px; }
        .log { background: #f5f5f5; padding: 10px; margin: 10px 0; max-height: 400px; overflow-y: auto; }
        .step { margin: 10px 0; padding: 10px; border-left: 3px solid #ccc; }
        .step.running { border-left-color: blue; }
        .step.success { border-left-color: green; }
        .step.error { border-left-color: red; }
    </style>
</head>
<body>
    <h1>Automated Login Test</h1>
    <p>Testing with account: <EMAIL>, password: ********</p>
    
    <button onclick="runFullTest()">Run Full Automated Test</button>
    <button onclick="clearLog()">Clear Log</button>
    
    <div id="test-log" class="log"></div>

    <!-- Hidden form elements for testing -->
    <div style="display: none;">
        <input type="email" id="login-email" value="<EMAIL>">
        <input type="password" id="login-password" value="********">
        <input type="password" id="site-password" value="">
        <div id="auth-status"></div>
        <div id="access-error"></div>
        <div id="loading-state" style="display: none;"></div>
        <div id="access-gate" style="display: none;"></div>
        <div id="main-app" class="main-app"></div>
    </div>

    <!-- Scripts -->
    <script src="js/utils.js"></script>
    <script src="js/crypto.js"></script>
    <script src="js/api.js"></script>
    <script src="js/auth.js"></script>

    <script>
        let testLog = [];
        
        function log(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = `[${timestamp}] ${message}`;
            testLog.push({timestamp, message, type});
            
            const logDiv = document.getElementById('test-log');
            logDiv.innerHTML += `<div class="${type}">${logEntry}</div>`;
            logDiv.scrollTop = logDiv.scrollHeight;
            
            console.log(logEntry);
        }

        function clearLog() {
            testLog = [];
            document.getElementById('test-log').innerHTML = '';
        }

        function createStep(title) {
            const logDiv = document.getElementById('test-log');
            const stepDiv = document.createElement('div');
            stepDiv.className = 'step running';
            stepDiv.innerHTML = `<strong>${title}</strong>`;
            logDiv.appendChild(stepDiv);
            logDiv.scrollTop = logDiv.scrollHeight;
            return stepDiv;
        }

        function updateStep(stepDiv, status, message = '') {
            stepDiv.className = `step ${status}`;
            if (message) {
                stepDiv.innerHTML += `<br>${message}`;
            }
        }

        async function runFullTest() {
            clearLog();
            log('Starting automated login test...', 'info');
            
            try {
                // Step 1: Initialize
                const step1 = createStep('Step 1: Initialize Application');
                await testInitialization();
                updateStep(step1, 'success', 'Initialization completed');

                // Step 2: Check Access Requirement
                const step2 = createStep('Step 2: Check Access Requirement');
                await testAccessRequirement();
                updateStep(step2, 'success', 'Access requirement check completed');

                // Step 3: Test getSalt
                const step3 = createStep('Step 3: Test getSalt API');
                await testGetSalt();
                updateStep(step3, 'success', 'getSalt test completed');

                // Step 4: Test Login API (if we have access token)
                const step4 = createStep('Step 4: Test Login API');
                await testLoginAPI();
                updateStep(step4, 'success', 'Login API test completed');

                // Step 5: Test Full Login Flow
                const step5 = createStep('Step 5: Test Full Login Flow');
                await testFullLogin();
                updateStep(step5, 'success', 'Full login test completed');

                log('All tests completed successfully!', 'success');

                // Summary
                log('=== TEST SUMMARY ===', 'info');
                log(`Access Token: ${window.accessToken ? 'AVAILABLE' : 'NOT AVAILABLE'}`, window.accessToken ? 'success' : 'error');
                if (window.accessToken) {
                    log('✓ Login should work now', 'success');
                } else {
                    log('✗ Login will fail - need to resolve access token issue', 'error');
                    log('Possible solutions:', 'info');
                    log('1. Server needs SITE_ACCESS_REQUIRED=false', 'info');
                    log('2. Or provide correct SITE_ACCESS_PASSWORD', 'info');
                }

            } catch (error) {
                log(`Test failed: ${error.message}`, 'error');
                console.error('Full error:', error);
            }
        }

        async function testInitialization() {
            log('Testing initialization...', 'info');
            
            // Update global aliases
            if (typeof updateGlobalAliases === 'function') {
                updateGlobalAliases();
                log('✓ Global aliases updated', 'success');
            } else {
                log('✗ updateGlobalAliases function not found', 'error');
            }

            // Initialize API manager
            if (typeof initializeAPI === 'function') {
                initializeAPI();
                log('✓ API manager initialized', 'success');
            } else {
                log('✗ initializeAPI function not found', 'error');
            }

            // Check state
            log(`API Manager: ${window.apiManager ? 'INITIALIZED' : 'NOT INITIALIZED'}`, 'info');
            log(`Access Token: ${window.accessToken ? 'SET' : 'NOT SET'}`, 'info');
        }

        async function testAccessRequirement() {
            log('Testing access requirement check...', 'info');

            // First, test the API directly
            try {
                log('Testing /api/check-access-requirement directly...', 'info');
                const response = await fetch('https://secure-notes-api.v8x.workers.dev/api/check-access-requirement');
                const data = await response.json();
                log(`Server response: ${JSON.stringify(data)}`, 'info');

                if (data.siteAccessRequired) {
                    log('⚠ Server requires site access password', 'warning');
                    log('Need to test site access verification...', 'info');
                    await testSiteAccessVerification();
                } else {
                    log('✓ Server does not require site access password', 'success');
                }
            } catch (error) {
                log(`✗ Direct API test failed: ${error.message}`, 'error');
            }

            if (typeof checkAccessRequirement === 'function') {
                await checkAccessRequirement();
                log('✓ checkAccessRequirement completed', 'success');
                log(`Access Token after check: ${window.accessToken ? 'SET' : 'NOT SET'}`, 'info');
                log(`Session Data: ${JSON.stringify(window.sessionData)}`, 'info');
            } else {
                log('✗ checkAccessRequirement function not found', 'error');
            }
        }

        async function testSiteAccessVerification() {
            log('Testing site access verification...', 'info');

            // Try with common passwords and empty password
            const testPasswords = ['', 'admin', 'password', '123456', 'test', 'secure', 'access', 'demo'];

            for (const password of testPasswords) {
                try {
                    log(`Trying password: "${password || '(empty)'}"...`, 'info');
                    const hashedInput = await sha256(password);
                    const response = await fetch('https://secure-notes-api.v8x.workers.dev/api/verify-access', {
                        method: 'POST',
                        headers: { 'Content-Type': 'application/json' },
                        body: JSON.stringify({ hash: hashedInput })
                    });

                    log(`Response status: ${response.status}`, 'info');
                    const result = await response.json();
                    log(`Response body: ${JSON.stringify(result)}`, 'info');

                    if (result.success) {
                        log(`✓ Site access password found: "${password || '(empty)'}"`, 'success');
                        window.accessToken = result.accessToken;
                        window.accessTokenExpiry = Date.now() + (2 * 60 * 60 * 1000);

                        // Update session data
                        window.sessionData.siteAccessVerified = true;
                        window.sessionData.accessTimestamp = Date.now();
                        window.sessionData.accessExpiry = Date.now() + (2 * 60 * 60 * 1000);

                        log(`Access token set: ${window.accessToken.substring(0, 20)}...`, 'success');
                        return;
                    } else {
                        log(`✗ Password "${password || '(empty)'}" failed: ${result.error || 'Unknown error'}`, 'error');
                    }
                } catch (error) {
                    log(`✗ Error testing password "${password || '(empty')": ${error.message}`, 'error');
                }
            }

            log('⚠ No valid site access password found', 'warning');
            log('⚠ This means the server requires a specific access password that we don\'t know', 'warning');
        }

        async function testGetSalt() {
            log('Testing getSalt API...', 'info');
            
            if (!window.apiManager) {
                throw new Error('API Manager not initialized');
            }

            try {
                const email = '<EMAIL>';
                log(`Calling getSalt for ${email}...`, 'info');
                const saltResponse = await window.apiManager.getSalt(email);
                log(`✓ getSalt successful: ${JSON.stringify(saltResponse)}`, 'success');
                return saltResponse;
            } catch (error) {
                log(`✗ getSalt failed: ${error.message}`, 'error');
                throw error;
            }
        }

        async function testLoginAPI() {
            log('Testing login API...', 'info');
            
            if (!window.accessToken) {
                log('⚠ No access token - login API will fail', 'warning');
                return;
            }

            try {
                // Get salt first
                const saltResponse = await window.apiManager.getSalt('<EMAIL>');
                const baseSalt = new Uint8Array(saltResponse.salt);
                
                // Derive auth key
                const authSalt = await CryptoUtils.deriveSubSalt(baseSalt, 'auth');
                const authKey = await CryptoUtils.deriveAuthKey('********', authSalt);
                
                log('Calling login API...', 'info');
                const result = await window.apiManager.login('<EMAIL>', authKey);
                log(`✓ Login API successful: ${JSON.stringify(result)}`, 'success');
                return result;
            } catch (error) {
                log(`✗ Login API failed: ${error.message}`, 'error');
                throw error;
            }
        }

        async function testFullLogin() {
            log('Testing full login flow...', 'info');
            
            if (typeof login === 'function') {
                try {
                    await login();
                    log('✓ Full login completed', 'success');
                } catch (error) {
                    log(`✗ Full login failed: ${error.message}`, 'error');
                    throw error;
                }
            } else {
                log('✗ login function not found', 'error');
            }
        }

        // Initialize on page load
        document.addEventListener('DOMContentLoaded', function() {
            log('Page loaded, ready for testing', 'info');
        });
    </script>
</body>
</html>
