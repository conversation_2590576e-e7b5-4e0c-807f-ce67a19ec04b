// Configuration and utility functions for SecureNotes - Extracted from index_old.html

// Security: Configuration constants with validation
const CONFIG = {
    API_BASE: 'https://secure-notes-api.v8x.workers.dev',
    ALLOWED_DOMAINS: ['secure-notes-api.v8x.workers.dev'],
    SITE_ACCESS_ENABLED: true, // Note: This will be dynamically determined by backend SITE_ACCESS_REQUIRED env var
    MAX_NOTE_LENGTH: 10000,
    SESSION_TIMEOUT:  60 * 60 * 1000, // 1 hours
    AUTO_LOGOUT_INACTIVE: 3 * 60 * 1000,  // 3 minutes inactive auto logout
    REQUEST_TIMEOUT: 30000, // 30 seconds
    MAX_RETRY_ATTEMPTS: 3,
    // Security: Add integrity check (SHA256 hash of domain string)
    EXPECTED_DOMAIN_HASH: '8b0e7c9c4b5f6a2d1e3f4a5b6c7d8e9f0a1b2c3d4e5f6a7b8c9d0e1f2a3b4c5d6' // Placeholder - will be updated with actual hash
};

// Security: Verify configuration integrity
async function verifyConfigIntegrity() {
    try {
        const domainStr = CONFIG.ALLOWED_DOMAINS.join(',');
        const hash = await sha256(domainStr);
        const expectedHash = CONFIG.EXPECTED_DOMAIN_HASH;

        // Simple integrity check (not foolproof but adds a layer)
        if (hash !== expectedHash) {
            // Configuration mismatch detected, but don't block functionality
            // In production, this could trigger additional security measures
        }
    } catch (error) {
        // Configuration integrity check failed silently
    }
}

// Security: Validate API endpoint
function validateApiEndpoint(url) {
    try {
        const urlObj = new URL(url);
        return CONFIG.ALLOWED_DOMAINS.includes(urlObj.hostname);
    } catch {
        return false;
    }
}

// Global state - use window object to avoid conflicts
if (typeof window.currentUser === 'undefined') {
    window.currentUser = null;
    window.encryptionKey = null;
    window.notes = [];
    window.editingNoteId = null;
    window.lastLoginAt = null;
    window.currentPage = 1;
    window.totalPages = 1;
    window.allNotes = [];
    window.decryptedNotes = new Map(); // Store decrypted content
    window.autoLogoutTimer = null;
    window.inactivityTimer = null;
    window.lastActivity = Date.now();
    window.accessToken = null; // Security: Store access token in memory only
    window.accessTokenExpiry = null; // Track token expiration
    window.sessionData = {
        siteAccessVerified: false,
        accessTimestamp: null,
        accessExpiry: null,
        lastActivity: Date.now()
    };
    window.isAdmin = false;
}

// Create aliases for easier access
let currentUser, encryptionKey, notes, editingNoteId, lastLoginAt;
let currentPage, totalPages, allNotes, decryptedNotes, autoLogoutTimer;
let inactivityTimer, lastActivity, accessToken, accessTokenExpiry;

function updateGlobalAliases() {
    currentUser = window.currentUser;
    encryptionKey = window.encryptionKey;
    notes = window.notes;
    editingNoteId = window.editingNoteId;
    lastLoginAt = window.lastLoginAt;
    currentPage = window.currentPage;
    totalPages = window.totalPages;
    allNotes = window.allNotes;
    decryptedNotes = window.decryptedNotes;
    autoLogoutTimer = window.autoLogoutTimer;
    inactivityTimer = window.inactivityTimer;
    lastActivity = window.lastActivity;
    accessToken = window.accessToken;
    accessTokenExpiry = window.accessTokenExpiry;
}

// Security: Login attempt tracking
let loginAttempts = new Map(); // email -> {count, lastAttempt, blocked}
const MAX_LOGIN_ATTEMPTS = 5;
const LOCKOUT_DURATION = 15 * 60 * 1000; // 15 minutes

// Security: Memory-only session management (enhanced security)
let sessionData = {
    siteAccessVerified: false,
    accessTimestamp: null,
    accessExpiry: null,
    lastActivity: Date.now()
};

// Security: Auto-lock configuration
const AUTO_LOCK_TIMEOUT = 3 * 60 * 1000; // 3 minutes
let autoLockTimer = null;

/**
 * Security fix: HTML escape function to prevent XSS attacks
 */
function escapeHTML(str) {
    if (typeof str !== 'string') return '';
    const div = document.createElement('div');
    div.textContent = str;
    return div.innerHTML;
}

/**
 * Security: Input validation functions
 */
function validateInput(input, type, maxLength = 1000) {
    if (!input || typeof input !== 'string') return false;
    if (input.length > maxLength) return false;

    switch (type) {
        case 'email':
            return /^[a-zA-Z0-9.!#$%&'*+/=?^_`{|}~-]+@[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(?:\.[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)*$/.test(input);
        case 'password':
            return input.length >= 8;
        case 'noteId':
            return /^\d+$/.test(input);
        default:
            return true;
    }
}

/**
 * Admin functionality
 */
let isAdmin = false;

async function checkAdminStatus() {
    try {
        const result = await window.apiManager.checkAdminStatus();

        if (result.success && result.isAdmin) {
            window.isAdmin = true;
            showAdminButton();
        } else {
            window.isAdmin = false;
        }
    } catch (error) {
        console.error('Admin check failed:', error);
        console.error('Error details:', error.message);
        isAdmin = false;
    }
}

function showAdminButton() {
    const adminBtn = document.getElementById('admin-btn');
    if (adminBtn) {
        adminBtn.style.display = 'inline-block';
    }
}

function hideAdminButton() {
    const adminBtn = document.getElementById('admin-btn');
    if (adminBtn) {
        adminBtn.style.display = 'none';
    }
}

function showAdminSection() {
    // Hide all sections first
    document.getElementById('notes-section').style.display = 'none';
    document.getElementById('settings-section').style.display = 'none';
    document.getElementById('admin-section').style.display = 'block';
    loadUsers();
}

function hideAdminSection() {
    // Hide admin section and show notes
    document.getElementById('admin-section').style.display = 'none';
    document.getElementById('settings-section').style.display = 'none';
    document.getElementById('notes-section').style.display = 'block';
}

async function loadUsers() {
    // Show loading state
    LoadingManager.showButtonLoading('refresh-users-btn');

    try {
        showStatus('backup-status-display', 'Loading user list...', 'info');

        const result = await apiManager.getUsers();
        if (result.success) {
            displayUsers(result.users);
            showStatus('backup-status-display', `Successfully loaded ${result.users.length} users`, 'success');
        } else {
            showStatus('backup-status-display', result.error || 'Failed to load user list', 'error');
        }
    } catch (error) {
        console.error('Load users error:', error);
        showStatus('backup-status-display', 'Failed to load user list', 'error');
    } finally {
        // Hide loading state
        LoadingManager.hideButtonLoading('refresh-users-btn');
    }
}

function displayUsers(users) {
    const usersList = document.getElementById('users-list');
    if (!users || users.length === 0) {
        usersList.innerHTML = '<div class="status-display">No user data available</div>';
        return;
    }

    let html = '<div class="admin-stats">';
    const activeUsers = users.filter(u => !u.is_disabled).length;
    const disabledUsers = users.filter(u => u.is_disabled).length;
    const totalNotes = users.reduce((sum, u) => sum + (u.note_count || 0), 0);

    html += `
        <div class="stat-card">
            <div class="stat-number">${users.length}</div>
            <div class="stat-label">Total Users</div>
        </div>
        <div class="stat-card">
            <div class="stat-number">${activeUsers}</div>
            <div class="stat-label">Active Users</div>
        </div>
        <div class="stat-card">
            <div class="stat-number">${disabledUsers}</div>
            <div class="stat-label">Disabled</div>
        </div>
        <div class="stat-card">
            <div class="stat-number">${totalNotes}</div>
            <div class="stat-label">Total Notes</div>
        </div>
    </div>`;

    users.forEach(user => {
        const isDisabled = user.is_disabled === 1;
        const createdDate = new Date(user.created_at).toLocaleDateString();

        html += `
            <div class="user-item ${isDisabled ? 'user-disabled' : ''}">
                <div class="user-info">
                    <div class="user-email">${escapeHTML(user.email)}</div>
                    <div class="user-meta">
                        Registration: ${createdDate} | Notes: ${user.note_count || 0}
                    </div>
                </div>
                <div class="user-actions">
                    <span class="user-status ${isDisabled ? 'disabled' : 'active'}">
                        ${isDisabled ? 'Disabled' : 'Active'}
                    </span>
                    <button class="toggle-user-btn ${isDisabled ? 'enable' : 'disable'}"
                            id="toggle-user-${user.id}"
                            onclick="toggleUser(${user.id}, ${!isDisabled})"
                            ${user.email === getCurrentAdminEmail() ? 'disabled title="Cannot disable your own account"' : ''}>
                        ${isDisabled ? 'Enable' : 'Disable'}
                    </button>
                </div>
            </div>
        `;
    });

    usersList.innerHTML = html;
}

function getCurrentAdminEmail() {
    // Get admin email from current login information
    // Simplified handling, should actually get from backend
    return window.currentUser || '';
}

async function toggleUser(userId, disable) {
    // Check if button is disabled (admin account protection)
    const buttonId = `toggle-user-${userId}`;
    const button = document.getElementById(buttonId);

    if (button && button.disabled && button.title === "Cannot disable your own account") {
        showStatus('backup-status-display', 'Cannot disable your own admin account', 'error');
        return;
    }

    if (disable && !confirm(`Are you sure you want to ${disable ? 'disable' : 'enable'} this user?`)) {
        return;
    }

    // Show loading state for the specific user button
    LoadingManager.showButtonLoading(buttonId);

    try {
        const result = await apiManager.toggleUserStatus(userId, disable);
        if (result.success) {
            showStatus('backup-status-display', result.message, 'success');
            loadUsers(); // Reload user list (this will hide the loading state)
        } else {
            showStatus('backup-status-display', result.error || 'Operation failed', 'error');
        }
    } catch (error) {
        console.error('Toggle user error:', error);
        showStatus('backup-status-display', 'Operation failed', 'error');
    } finally {
        // Hide loading state (in case loadUsers() doesn't get called)
        LoadingManager.hideButtonLoading(buttonId);
    }
}

async function loadBackupStatus() {
    // Show loading state
    LoadingManager.showButtonLoading('backup-status-btn');

    try {
        showStatus('backup-status-display', 'Querying backup status...', 'info');

        const result = await apiManager.getBackupStatus();
        if (result.success) {
            const provider = result.providerStatus || {};
            let providerDetails = '';

            // Generate provider-specific status details
            if (provider.provider === 'Backblaze B2') {
                providerDetails = `
                    <p><strong>Application Key ID:</strong> ${provider.hasApplicationKeyId ? 'Configured' : 'Not configured'}</p>
                    <p><strong>Application Key:</strong> ${provider.hasApplicationKey ? 'Configured' : 'Not configured'}</p>
                    <p><strong>Bucket ID:</strong> ${provider.hasBucketId ? 'Configured' : 'Not configured'}</p>
                `;
            } else if (provider.provider === 'Google Drive') {
                providerDetails = `
                    <p><strong>Folder ID:</strong> ${provider.hasFolderId ? 'Configured' : 'Not configured'}</p>
                    <p><strong>Credentials:</strong> ${provider.hasCredentials ? 'Configured' : 'Not configured'}</p>
                `;
            } else if (provider.provider === 'OneDrive') {
                providerDetails = `
                    <p><strong>Drive ID:</strong> ${provider.hasDriveId ? 'Configured' : 'Not configured'}</p>
                    <p><strong>Credentials:</strong> ${provider.hasCredentials ? 'Configured' : 'Not configured'}</p>
                `;
            }

            const statusHtml = `
                <h5>Backup Configuration Status</h5>
                <p><strong>Backup Provider:</strong> ${result.backupProvider || 'Not specified'}</p>
                <p><strong>Provider:</strong> ${provider.provider || 'Unknown'}</p>
                <p><strong>Backup Enabled:</strong> ${result.backupEnabled ? 'Yes' : 'No'}</p>
                <p><strong>Retention Count:</strong> ${result.retentionCount} backups</p>
                ` + providerDetails + `
                <p><strong>Last Backup Location:</strong> ${provider.lastBackupLocation || 'Check your backup provider'}</p>
            `;
            document.getElementById('backup-status-display').innerHTML = statusHtml;
        } else {
            showStatus('backup-status-display', result.error || 'Failed to query backup status', 'error');
        }
    } catch (error) {
        console.error('Load backup status error:', error);
        showStatus('backup-status-display', 'Failed to query backup status', 'error');
    } finally {
        // Hide loading state
        LoadingManager.hideButtonLoading('backup-status-btn');
    }
}

async function triggerManualBackup() {
    if (!confirm('Are you sure you want to perform a manual backup? This operation may take some time.')) {
        return;
    }

    // Show loading state
    LoadingManager.showButtonLoading('manual-backup-btn');

    try {
        showStatus('backup-status-display', 'Performing backup...', 'info');

        const result = await apiManager.triggerBackup();
        if (result.success) {
            showStatus('backup-status-display', 'Backup completed successfully!', 'success');
        } else {
            showStatus('backup-status-display', result.error || 'Backup failed', 'error');
        }
    } catch (error) {
        console.error('Manual backup error:', error);
        showStatus('backup-status-display', 'Backup failed', 'error');
    } finally {
        // Hide loading state
        LoadingManager.hideButtonLoading('manual-backup-btn');
    }
}

let selectedBackupFileId = null;

async function showRestoreBackupModal() {
    const modal = document.getElementById('restore-backup-modal');
    const filesList = document.getElementById('backup-files-list');
    const confirmBtn = document.getElementById('restore-confirm-btn');

    // Reset state
    selectedBackupFileId = null;
    confirmBtn.disabled = true;

    // Show modal
    modal.style.display = 'flex';

    // Load backup files
    filesList.innerHTML = '<p>Loading backup files...</p>';

    try {
        const result = await apiManager.getBackupFilesList();
        if (result.success && result.files && result.files.length > 0) {
            displayBackupFiles(result.files, result.provider);
        } else {
            filesList.innerHTML = '<p>No backup files found.</p>';
        }
    } catch (error) {
        console.error('Failed to load backup files:', error);
        filesList.innerHTML = '<p class="error">Failed to load backup files. Please try again.</p>';
    }
}

function displayBackupFiles(files, provider) {
    const filesList = document.getElementById('backup-files-list');

    let html = `<p><strong>Backup Provider:</strong> ${provider}</p>`;
    html += '<div class="backup-files-grid">';

    files.forEach(file => {
        const date = new Date(file.uploadTimestamp);
        const formattedDate = date.toLocaleString();
        const fileSize = formatFileSize(file.size);

        html += `
            <div class="backup-file-item" data-file-id="${escapeHTML(file.fileId)}" onclick="selectBackupFile('${escapeHTML(file.fileId)}', this)">
                <div class="backup-file-name">${escapeHTML(file.fileName)}</div>
                <div class="backup-file-details">
                    <span>📅 ${formattedDate}</span>
                    <span>📦 ${fileSize}</span>
                </div>
            </div>
        `;
    });

    html += '</div>';
    filesList.innerHTML = html;
}

function selectBackupFile(fileId, element) {
    // Remove previous selection
    document.querySelectorAll('.backup-file-item').forEach(item => {
        item.classList.remove('selected');
    });

    // Select current item
    element.classList.add('selected');
    selectedBackupFileId = fileId;

    // Enable confirm button
    document.getElementById('restore-confirm-btn').disabled = false;
}

function hideRestoreBackupModal() {
    document.getElementById('restore-backup-modal').style.display = 'none';
    selectedBackupFileId = null;
}

async function confirmRestoreBackup() {
    if (!selectedBackupFileId) {
        alert('Please select a backup file to restore.');
        return;
    }

    const finalConfirm = confirm(
        '⚠️ FINAL WARNING ⚠️\n\n' +
        'This will PERMANENTLY DELETE all current data and replace it with the selected backup.\n\n' +
        'This action CANNOT be undone!\n\n' +
        'Are you absolutely sure you want to proceed?'
    );

    if (!finalConfirm) {
        return;
    }

    // Show loading state
    const confirmBtn = document.getElementById('restore-confirm-btn');
    const originalText = confirmBtn.textContent;
    confirmBtn.disabled = true;
    confirmBtn.textContent = 'Restoring...';

    try {
        const result = await apiManager.restoreFromBackup(selectedBackupFileId, 'RESTORE_DATABASE_FROM_BACKUP');

        if (result.success) {
            alert('Database restored successfully!\n\nYou will be logged out and need to log in again.');
            hideRestoreBackupModal();
            logout(); // Force logout to refresh session
        } else {
            alert('Restore failed: ' + (result.error || 'Unknown error'));
        }
    } catch (error) {
        console.error('Restore error:', error);
        alert('Restore failed: ' + error.message);
    } finally {
        confirmBtn.disabled = false;
        confirmBtn.textContent = originalText;
    }
}

function formatFileSize(bytes) {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

/**
 * Password visibility toggle function
 */
function togglePasswordVisibility(inputId) {
    const input = document.getElementById(inputId);
    const toggle = input.parentElement.querySelector('.password-toggle');

    if (input.type === 'password') {
        input.type = 'text';
        toggle.textContent = '🙈'; // Hide icon
    } else {
        input.type = 'password';
        toggle.textContent = '👁️'; // Show icon
    }
}

/**
 * Password strength analysis
 */
function analyzePasswordStrength(password) {
    const requirements = {
        length: password.length >= 12,
        uppercase: /[A-Z]/.test(password),
        lowercase: /[a-z]/.test(password),
        numbers: /\d/.test(password),
        symbols: /[!@#$%^&*(),.?":{}|<>]/.test(password)
    };

    const score = Object.values(requirements).filter(Boolean).length;
    const hasMinLength = password.length >= 8;

    return {
        requirements,
        score,
        hasMinLength,
        isStrong: score >= 4 && requirements.length,
        isWeak: score <= 2 || password.length < 8
    };
}

/**
 * Update password strength indicator
 */
function updatePasswordStrengthIndicator(password) {
    const hintElement = document.getElementById('password-strength-hint');
    const requirementsList = document.getElementById('password-requirements-list');

    if (!password || password.length === 0) {
        hintElement.style.display = 'none';
        return;
    }

    hintElement.style.display = 'block';
    const analysis = analyzePasswordStrength(password);

    // Update requirement indicators
    const reqElements = {
        length: document.getElementById('req-length'),
        uppercase: document.getElementById('req-uppercase'),
        lowercase: document.getElementById('req-lowercase'),
        numbers: document.getElementById('req-numbers'),
        symbols: document.getElementById('req-symbols')
    };

    // Update each requirement
    reqElements.length.textContent = analysis.requirements.length ? '✓ At least 12 characters' : '✗ At least 12 characters (current minimum: 8)';
    reqElements.length.className = analysis.requirements.length ? 'requirement-met' : 'requirement-unmet';

    reqElements.uppercase.textContent = analysis.requirements.uppercase ? '✓ Uppercase letters (A-Z)' : '✗ Uppercase letters (A-Z)';
    reqElements.uppercase.className = analysis.requirements.uppercase ? 'requirement-met' : 'requirement-unmet';

    reqElements.lowercase.textContent = analysis.requirements.lowercase ? '✓ Lowercase letters (a-z)' : '✗ Lowercase letters (a-z)';
    reqElements.lowercase.className = analysis.requirements.lowercase ? 'requirement-met' : 'requirement-unmet';

    reqElements.numbers.textContent = analysis.requirements.numbers ? '✓ Numbers (0-9)' : '✗ Numbers (0-9)';
    reqElements.numbers.className = analysis.requirements.numbers ? 'requirement-met' : 'requirement-unmet';

    reqElements.symbols.textContent = analysis.requirements.symbols ? '✓ Special characters (!@#$%^&*)' : '✗ Special characters (!@#$%^&*)';
    reqElements.symbols.className = analysis.requirements.symbols ? 'requirement-met' : 'requirement-unmet';

    // Update hint style based on strength
    hintElement.className = 'password-hint';
    if (analysis.isStrong) {
        hintElement.classList.add('success');
    } else if (analysis.isWeak) {
        hintElement.classList.add('danger');
    } else {
        hintElement.classList.add('warning');
    }
}

/**
 * Security: Check if account is temporarily locked due to failed attempts
 */
function isAccountLocked(email) {
    const attempts = loginAttempts.get(email);
    if (!attempts) return false;

    const now = Date.now();
    if (attempts.blocked && (now - attempts.lastAttempt) < LOCKOUT_DURATION) {
        return true;
    }

    // Reset if lockout period has passed
    if (attempts.blocked && (now - attempts.lastAttempt) >= LOCKOUT_DURATION) {
        loginAttempts.delete(email);
        return false;
    }

    return false;
}

/**
 * Security: Record failed login attempt
 */
function recordFailedLogin(email) {
    const now = Date.now();
    const attempts = loginAttempts.get(email) || { count: 0, lastAttempt: 0, blocked: false };

    attempts.count++;
    attempts.lastAttempt = now;

    if (attempts.count >= MAX_LOGIN_ATTEMPTS) {
        attempts.blocked = true;
    }

    loginAttempts.set(email, attempts);
}

/**
 * Security: Clear login attempts on successful login
 */
function clearLoginAttempts(email) {
    loginAttempts.delete(email);
}

/**
 * Security: Get remaining lockout time
 */
function getRemainingLockoutTime(email) {
    const attempts = loginAttempts.get(email);
    if (!attempts || !attempts.blocked) return 0;

    const elapsed = Date.now() - attempts.lastAttempt;
    const remaining = LOCKOUT_DURATION - elapsed;
    return Math.max(0, remaining);
}

// Loading state management
const LoadingManager = {
    activeOperations: new Set(),

    // Show loading state for a button
    showButtonLoading(buttonId) {
        const button = document.getElementById(buttonId);
        if (button) {
            button.classList.add('loading');
            button.disabled = true;
            this.activeOperations.add(buttonId);
            this.showProgressBar();
        }
    },

    // Hide loading state for a button
    hideButtonLoading(buttonId) {
        const button = document.getElementById(buttonId);
        if (button) {
            button.classList.remove('loading');

            // Check if button should remain disabled (e.g., admin account disable button)
            const shouldStayDisabled = button.hasAttribute('data-permanently-disabled') ||
                                     button.title === "Cannot disable your own account";

            if (!shouldStayDisabled) {
                button.disabled = false;
            }

            this.activeOperations.delete(buttonId);

            if (this.activeOperations.size === 0) {
                this.hideProgressBar();
            }
        }
    },

    // Show loading state for general operations
    showLoading(operationId) {
        this.activeOperations.add(operationId);
        this.showProgressBar();
    },

    // Hide loading state for general operations
    hideLoading(operationId) {
        this.activeOperations.delete(operationId);
        if (this.activeOperations.size === 0) {
            this.hideProgressBar();
        }
    },

    // Show progress bar
    showProgressBar() {
        const progressBar = document.getElementById('progress-bar');
        if (progressBar) {
            progressBar.classList.add('active');
        }
    },

    // Hide progress bar
    hideProgressBar() {
        const progressBar = document.getElementById('progress-bar');
        if (progressBar) {
            progressBar.classList.remove('active');
        }
    }
};

function showStatus(elementId, message, type = 'info') {
    const statusEl = document.getElementById(elementId);
    if (message) {
        const safeMessage = escapeHTML(message);
        statusEl.innerHTML = `<div class="status ${type}">${safeMessage}</div>`;
        if (type !== 'info' || message.includes('Loading')) {
            setTimeout(() => {
                statusEl.innerHTML = '';
            }, 5000);
        }
    } else {
        statusEl.innerHTML = '';
    }
}

function showAuthSection() {
    // For index.html - just ensure auth section is visible
    const authSection = document.getElementById('auth-section');
    if (authSection) {
        authSection.style.display = 'block';
    }

    // Hide other sections if they exist (for compatibility with other pages)
    const notesSection = document.getElementById('notes-section');
    const settingsSection = document.getElementById('settings-section');
    const adminSection = document.getElementById('admin-section');

    if (notesSection) notesSection.style.display = 'none';
    if (settingsSection) settingsSection.style.display = 'none';
    if (adminSection) adminSection.style.display = 'none';
}

function showNotesSection() {
    // Hide all sections first
    document.getElementById('auth-section').style.display = 'none';
    document.getElementById('settings-section').style.display = 'none';
    document.getElementById('admin-section').style.display = 'none';

    // Show notes section
    document.getElementById('notes-section').style.display = 'block';
    document.getElementById('notes-section').classList.add('active');

    // Load notes if user is logged in
    if (window.currentUser && window.encryptionKey) {
        loadNotes();
    }
}

function showSettingsSection() {
    // Hide all sections first
    document.getElementById('auth-section').style.display = 'none';
    document.getElementById('notes-section').style.display = 'none';
    document.getElementById('admin-section').style.display = 'none';

    // Show settings section
    document.getElementById('settings-section').style.display = 'block';
    document.getElementById('settings-section').classList.add('active');

    // Load settings data
    if (typeof load2FAStatus === 'function') {
        load2FAStatus();
    }

    // Update account email display
    const accountEmailEl = document.getElementById('account-email');
    if (accountEmailEl && window.currentUser) {
        accountEmailEl.textContent = window.currentUser;
    }
}

/**
 * Security: Secure API request function with validation
 */
async function secureApiRequest(endpoint, options = {}) {
    const url = `${CONFIG.API_BASE}${endpoint}`;

    // Validate API endpoint
    if (!validateApiEndpoint(url)) {
        throw new Error('Invalid API endpoint');
    }

    const headers = {
        'Content-Type': 'application/json',
        ...options.headers
    };

    // Special cases: These endpoints don't need access token
    if (endpoint === '/api/verify-access' || endpoint === '/api/check-access-requirement' || endpoint.startsWith('/api/salt')) {
        // No access token required for these endpoints
    } else {
        // For all other endpoints, check access token
        if (window.accessToken && window.accessTokenExpiry && Date.now() < window.accessTokenExpiry) {
            headers['X-Access-Token'] = window.accessToken;
        } else if (window.accessToken && window.accessTokenExpiry && Date.now() >= window.accessTokenExpiry) {
            // Access token expired, clear it and re-check access requirements
            window.accessToken = null;
            window.accessTokenExpiry = null;
            window.sessionData.siteAccessVerified = false;
            window.sessionData.accessTimestamp = null;
            window.sessionData.accessExpiry = null;

            // Hide main app and show loading state
            const mainApp = document.getElementById('main-app');
            const accessGate = document.getElementById('access-gate');
            const loadingState = document.getElementById('loading-state');
            if (mainApp) mainApp.classList.remove('show');
            if (accessGate) accessGate.style.display = 'none';
            if (loadingState) loadingState.style.display = 'block';

            // Re-check access requirements
            setTimeout(() => {
                if (typeof checkAccessRequirement === 'function') {
                    checkAccessRequirement();
                }
            }, 100);

            throw new Error('Access session expired. Re-checking access requirements...');
        } else {
            // No access token available - this should not happen for normal API calls
            // This indicates the user needs to complete site access verification first
            throw new Error('Site access verification required. Please refresh the page and verify access.');
        }
    }

    const requestOptions = {
        method: 'GET',
        headers,
        ...options
    };

    // Add timeout
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), CONFIG.REQUEST_TIMEOUT);

    try {
        requestOptions.signal = controller.signal;
        const response = await fetch(url, requestOptions);
        clearTimeout(timeoutId);

        if (!response.ok) {
            const error = await response.json().catch(() => ({ error: 'Request failed' }));
            throw new Error(error.error || 'Request failed');
        }

        return await response.json();
    } catch (error) {
        clearTimeout(timeoutId);
        if (error.name === 'AbortError') {
            throw new Error('Request timeout');
        }
        throw error;
    }
}

/**
 * SHA-256 hash function for password hashing
 */
async function sha256(message) {
    const msgBuffer = new TextEncoder().encode(message);
    const hashBuffer = await crypto.subtle.digest('SHA-256', msgBuffer);
    const hashArray = Array.from(new Uint8Array(hashBuffer));
    return hashArray.map(b => b.toString(16).padStart(2, '0')).join('');
}

/**
 * Auto-logout timer functions
 */
function resetAutoLogoutTimer() {
    window.lastActivity = Date.now();
}

function startAutoLogoutTimer() {
    function checkInactivity() {
        if (Date.now() - window.lastActivity > CONFIG.AUTO_LOGOUT_INACTIVE) {
            if (typeof logout === 'function') {
                logout();
                alert('You have been automatically logged out due to inactivity');
            }
        }
    }

    window.inactivityTimer = setInterval(checkInactivity, 60000); // Check every minute

    // Listen for user activity
    ['mousedown', 'mousemove', 'keypress', 'scroll', 'touchstart'].forEach(event => {
        document.addEventListener(event, () => {
            window.lastActivity = Date.now();
        }, { passive: true });
    });
}

/**
 * Show status message in specified element
 */
function showStatus(elementId, message, type = 'info') {
    const statusEl = document.getElementById(elementId);
    if (!statusEl) return;

    statusEl.textContent = message;
    statusEl.className = `status ${type}`;
    statusEl.style.display = message ? 'block' : 'none';
}

/**
 * Clear session data
 */
function clearSessionData() {
    // Clear memory-only session data
    window.sessionData.siteAccessVerified = false;
    window.sessionData.accessTimestamp = null;
    window.sessionData.accessExpiry = null;
    window.sessionData.lastActivity = Date.now();

    // Clear access tokens
    window.accessToken = null;
    window.accessTokenExpiry = null;
}

/**
 * Password strength analysis
 */
function analyzePasswordStrength(password) {
    const requirements = {
        length: password.length >= 12,
        uppercase: /[A-Z]/.test(password),
        lowercase: /[a-z]/.test(password),
        numbers: /\d/.test(password),
        symbols: /[!@#$%^&*(),.?":{}|<>]/.test(password)
    };

    const score = Object.values(requirements).filter(Boolean).length;
    const hasMinLength = password.length >= 8;

    return {
        requirements,
        score,
        hasMinLength,
        isStrong: score >= 4 && requirements.length,
        isWeak: score <= 2 || password.length < 8
    };
}

/**
 * Update password strength indicator
 */
function updatePasswordStrengthIndicator(password) {
    const hintElement = document.getElementById('password-strength-hint');
    const requirementsList = document.getElementById('password-requirements-list');

    if (!password || password.length === 0) {
        hintElement.style.display = 'none';
        return;
    }

    hintElement.style.display = 'block';
    const analysis = analyzePasswordStrength(password);

    // Update requirement indicators
    const reqElements = {
        length: document.getElementById('req-length'),
        uppercase: document.getElementById('req-uppercase'),
        lowercase: document.getElementById('req-lowercase'),
        numbers: document.getElementById('req-numbers'),
        symbols: document.getElementById('req-symbols')
    };

    // Update each requirement
    if (reqElements.length) {
        reqElements.length.textContent = analysis.requirements.length ? '✓ At least 12 characters' : '✗ At least 12 characters (current minimum: 8)';
        reqElements.length.className = analysis.requirements.length ? 'requirement-met' : 'requirement-unmet';
    }

    if (reqElements.uppercase) {
        reqElements.uppercase.textContent = analysis.requirements.uppercase ? '✓ Uppercase letters (A-Z)' : '✗ Uppercase letters (A-Z)';
        reqElements.uppercase.className = analysis.requirements.uppercase ? 'requirement-met' : 'requirement-unmet';
    }

    if (reqElements.lowercase) {
        reqElements.lowercase.textContent = analysis.requirements.lowercase ? '✓ Lowercase letters (a-z)' : '✗ Lowercase letters (a-z)';
        reqElements.lowercase.className = analysis.requirements.lowercase ? 'requirement-met' : 'requirement-unmet';
    }

    if (reqElements.numbers) {
        reqElements.numbers.textContent = analysis.requirements.numbers ? '✓ Numbers (0-9)' : '✗ Numbers (0-9)';
        reqElements.numbers.className = analysis.requirements.numbers ? 'requirement-met' : 'requirement-unmet';
    }

    if (reqElements.symbols) {
        reqElements.symbols.textContent = analysis.requirements.symbols ? '✓ Special characters (!@#$%^&*)' : '✗ Special characters (!@#$%^&*)';
        reqElements.symbols.className = analysis.requirements.symbols ? 'requirement-met' : 'requirement-unmet';
    }

    // Update hint style based on strength
    hintElement.className = 'password-hint';
    if (analysis.isStrong) {
        hintElement.classList.add('success');
    } else if (analysis.isWeak) {
        hintElement.classList.add('danger');
    } else {
        hintElement.classList.add('warning');
    }
}

/**
 * Security: Check if account is temporarily locked due to failed attempts
 */
function isAccountLocked(email) {
    const attempts = loginAttempts.get(email);
    if (!attempts) return false;

    const now = Date.now();
    if (attempts.blocked && (now - attempts.lastAttempt) < LOCKOUT_DURATION) {
        return true;
    }

    // Reset if lockout period has passed
    if (attempts.blocked && (now - attempts.lastAttempt) >= LOCKOUT_DURATION) {
        loginAttempts.delete(email);
        return false;
    }

    return false;
}

/**
 * Security: Record failed login attempt
 */
function recordFailedLogin(email) {
    const now = Date.now();
    const attempts = loginAttempts.get(email) || {count: 0, lastAttempt: 0, blocked: false};

    attempts.count++;
    attempts.lastAttempt = now;

    if (attempts.count >= MAX_LOGIN_ATTEMPTS) {
        attempts.blocked = true;
        showStatus('auth-status', `Account temporarily locked due to too many failed attempts. Try again in 15 minutes.`, 'error');
    }

    loginAttempts.set(email, attempts);
}

/**
 * Security: Clear login attempts on successful login
 */
function clearLoginAttempts(email) {
    loginAttempts.delete(email);
}

/**
 * Hide admin button
 */
function hideAdminButton() {
    const adminBtn = document.getElementById('admin-btn');
    if (adminBtn) {
        adminBtn.style.display = 'none';
    }
}