// Admin panel functionality

// Load system statistics
async function loadSystemStats() {
    try {
        showLoading('load-admin');
        const stats = await apiManager.getSystemStats();
        
        document.getElementById('total-users').textContent = stats.totalUsers || '0';
        document.getElementById('total-notes').textContent = stats.totalNotes || '0';
        document.getElementById('active-sessions').textContent = stats.activeSessions || '0';
        document.getElementById('system-health').textContent = stats.systemHealth || 'Unknown';
        
        hideLoading('load-admin');
    } catch (error) {
        hideLoading('load-admin');
        showStatus('admin-status', 'Error loading system stats: ' + error.message, 'error');
    }
}

// Load users list
async function loadUsers() {
    try {
        showLoading('load-users');
        const users = await apiManager.getUsers();
        
        const usersList = document.getElementById('users-list');
        
        if (!users || users.length === 0) {
            usersList.innerHTML = '<p>No users found</p>';
            hideLoading('load-users');
            return;
        }
        
        usersList.innerHTML = users.map(user => `
            <div class="user-item">
                <div class="user-info">
                    <div class="user-email">${escapeHtml(user.email)}</div>
                    <div class="user-details">
                        <span>Created: ${new Date(user.createdAt).toLocaleDateString()}</span>
                        <span>Notes: ${user.noteCount || 0}</span>
                        <span>Status: ${user.isActive ? 'Active' : 'Inactive'}</span>
                        ${user.is2FAEnabled ? '<span class="status-indicator status-enabled">2FA</span>' : ''}
                    </div>
                </div>
                <div class="user-actions">
                    <button class="btn btn-sm" onclick="viewUserDetails('${user.id}')">Details</button>
                    <button class="btn btn-sm btn-danger" onclick="suspendUser('${user.id}')">
                        ${user.isActive ? 'Suspend' : 'Activate'}
                    </button>
                </div>
            </div>
        `).join('');
        
        hideLoading('load-users');
    } catch (error) {
        hideLoading('load-users');
        showStatus('admin-status', 'Error loading users: ' + error.message, 'error');
    }
}

// Load system logs
async function loadLogs() {
    try {
        showLoading('load-logs');
        const logLevel = document.getElementById('log-level').value;
        const logs = await apiManager.getSystemLogs(logLevel);
        
        const logsContainer = document.getElementById('logs-container');
        
        if (!logs || logs.length === 0) {
            logsContainer.innerHTML = '<p>No logs found</p>';
            hideLoading('load-logs');
            return;
        }
        
        logsContainer.innerHTML = logs.map(log => `
            <div class="log-entry log-${log.level}">
                <div class="log-timestamp">${new Date(log.timestamp).toLocaleString()}</div>
                <div class="log-level">${log.level.toUpperCase()}</div>
                <div class="log-message">${escapeHtml(log.message)}</div>
                ${log.details ? `<div class="log-details">${escapeHtml(JSON.stringify(log.details))}</div>` : ''}
            </div>
        `).join('');
        
        hideLoading('load-logs');
    } catch (error) {
        hideLoading('load-logs');
        showStatus('admin-status', 'Error loading logs: ' + error.message, 'error');
    }
}

// Load system configuration
async function loadConfiguration() {
    try {
        const config = await apiManager.getSystemConfiguration();
        
        document.getElementById('max-users').value = config.maxUsers || 1000;
        document.getElementById('session-timeout').value = config.sessionTimeout || 60;
        document.getElementById('max-notes-per-user').value = config.maxNotesPerUser || 1000;
        document.getElementById('maintenance-mode').value = config.maintenanceMode ? 'true' : 'false';
        
    } catch (error) {
        showStatus('admin-status', 'Error loading configuration: ' + error.message, 'error');
    }
}

// Save system configuration
async function saveConfiguration() {
    try {
        const config = {
            maxUsers: parseInt(document.getElementById('max-users').value),
            sessionTimeout: parseInt(document.getElementById('session-timeout').value),
            maxNotesPerUser: parseInt(document.getElementById('max-notes-per-user').value),
            maintenanceMode: document.getElementById('maintenance-mode').value === 'true'
        };
        
        await apiManager.updateSystemConfiguration(config);
        showStatus('admin-status', 'Configuration saved successfully', 'success');
        
    } catch (error) {
        showStatus('admin-status', 'Error saving configuration: ' + error.message, 'error');
    }
}

// Load backup status
async function loadBackupStatus() {
    try {
        const status = await apiManager.getBackupStatus();
        
        document.getElementById('last-backup').textContent = status.lastBackup ? 
            new Date(status.lastBackup).toLocaleString() : 'Never';
        document.getElementById('backup-size').textContent = status.backupSize || 'Unknown';
        
    } catch (error) {
        showStatus('admin-status', 'Error loading backup status: ' + error.message, 'error');
    }
}

// Create backup
async function createBackup() {
    if (!confirm('Create a new system backup? This may take a few minutes.')) {
        return;
    }
    
    try {
        showLoading('load-admin');
        await apiManager.createBackup();
        
        showStatus('admin-status', 'Backup created successfully', 'success');
        loadBackupStatus();
        hideLoading('load-admin');
        
    } catch (error) {
        hideLoading('load-admin');
        showStatus('admin-status', 'Error creating backup: ' + error.message, 'error');
    }
}

// Download backup
async function downloadBackup() {
    try {
        const backupData = await apiManager.downloadBackup();
        
        // Create download link
        const blob = new Blob([JSON.stringify(backupData, null, 2)], { type: 'application/json' });
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `securenotes-backup-${new Date().toISOString().split('T')[0]}.json`;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        URL.revokeObjectURL(url);
        
        showStatus('admin-status', 'Backup downloaded successfully', 'success');
        
    } catch (error) {
        showStatus('admin-status', 'Error downloading backup: ' + error.message, 'error');
    }
}

// Perform maintenance
async function performMaintenance() {
    if (!confirm('Run system maintenance? This will clean up old sessions and optimize the database.')) {
        return;
    }
    
    try {
        showLoading('load-admin');
        const result = await apiManager.performMaintenance();
        
        showStatus('admin-status', `Maintenance completed: ${result.message}`, 'success');
        loadSystemStats();
        hideLoading('load-admin');
        
    } catch (error) {
        hideLoading('load-admin');
        showStatus('admin-status', 'Error performing maintenance: ' + error.message, 'error');
    }
}

// Clear logs
async function clearLogs() {
    if (!confirm('Clear all system logs? This action cannot be undone.')) {
        return;
    }
    
    try {
        await apiManager.clearSystemLogs();
        showStatus('admin-status', 'Logs cleared successfully', 'success');
        loadLogs();
        
    } catch (error) {
        showStatus('admin-status', 'Error clearing logs: ' + error.message, 'error');
    }
}

// View user details
async function viewUserDetails(userId) {
    try {
        const user = await apiManager.getUserDetails(userId);
        
        const details = `
            Email: ${user.email}
            Created: ${new Date(user.createdAt).toLocaleString()}
            Last Login: ${user.lastLogin ? new Date(user.lastLogin).toLocaleString() : 'Never'}
            Notes Count: ${user.noteCount || 0}
            2FA Enabled: ${user.is2FAEnabled ? 'Yes' : 'No'}
            Status: ${user.isActive ? 'Active' : 'Suspended'}
        `;
        
        alert(details);
        
    } catch (error) {
        showStatus('admin-status', 'Error loading user details: ' + error.message, 'error');
    }
}

// Suspend/activate user
async function suspendUser(userId) {
    try {
        const result = await apiManager.toggleUserStatus(userId);
        
        showStatus('admin-status', `User ${result.isActive ? 'activated' : 'suspended'} successfully`, 'success');
        loadUsers();
        
    } catch (error) {
        showStatus('admin-status', 'Error updating user status: ' + error.message, 'error');
    }
}

// Export user data
async function exportUserData() {
    try {
        const data = await apiManager.exportUserData();
        
        // Create download link
        const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' });
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `user-data-export-${new Date().toISOString().split('T')[0]}.json`;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        URL.revokeObjectURL(url);
        
        showStatus('admin-status', 'User data exported successfully', 'success');
        
    } catch (error) {
        showStatus('admin-status', 'Error exporting user data: ' + error.message, 'error');
    }
}

// Logout function
function logout() {
    // Clear sensitive data
    if (encryptionKey) {
        CryptoUtils.clearSensitiveData(encryptionKey);
    }
    
    // Clear session
    currentUser = null;
    encryptionKey = null;
    
    if (apiManager) {
        apiManager.clearToken();
    }
    
    // Redirect to login
    window.location.href = 'index.html';
}
