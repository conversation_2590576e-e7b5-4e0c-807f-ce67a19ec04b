<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="Content-Security-Policy" content="default-src 'self'; script-src 'self' 'unsafe-inline'; style-src 'self' 'unsafe-inline'; connect-src 'self' https://secure-notes-api.v8x.workers.dev; img-src 'self' data:; font-src 'self'">
    <meta http-equiv="Strict-Transport-Security" content="max-age=31536000; includeSubDomains">
    <meta http-equiv="X-Content-Type-Options" content="nosniff">
    <meta http-equiv="Referrer-Policy" content="strict-origin-when-cross-origin">
    <title>SecureNotes - Settings</title>
    <link rel="stylesheet" href="styles/common.css">
    <style>
        .settings-section {
            background: #f8f9fa;
            border-radius: 12px;
            padding: 20px;
            margin-bottom: 20px;
            border-left: 4px solid #667eea;
        }

        .settings-section h3 {
            color: #667eea;
            margin-bottom: 15px;
            font-size: 1.2em;
        }

        .qr-code-container {
            text-align: center;
            margin: 20px 0;
        }

        .backup-codes {
            background: #fff;
            border: 1px solid #e1e5e9;
            border-radius: 8px;
            padding: 15px;
            margin: 15px 0;
            font-family: monospace;
            font-size: 14px;
            line-height: 1.6;
        }

        .backup-codes h4 {
            margin-bottom: 10px;
            color: #dc3545;
            font-family: inherit;
        }

        .backup-code {
            display: block;
            margin: 5px 0;
            padding: 5px;
            background: #f8f9fa;
            border-radius: 4px;
        }

        .status-indicator {
            display: inline-block;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: 500;
            margin-left: 10px;
        }

        .status-enabled {
            background: #d4edda;
            color: #155724;
        }

        .status-disabled {
            background: #f8d7da;
            color: #721c24;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔐 SecureNotes</h1>
            <p>Account Settings & Security</p>
        </div>

        <!-- Navigation -->
        <div class="nav-tabs">
            <a href="notes.html" class="nav-tab">My Notes</a>
            <button class="nav-tab active">Settings</button>
            <a href="admin.html" class="nav-tab" id="admin-nav" style="display: none;">Admin</a>
            <button class="nav-tab" onclick="logout()">Logout</button>
        </div>

        <!-- Settings Status -->
        <div id="settings-status" class="status"></div>

        <!-- Password Change Section -->
        <div class="settings-section">
            <h3>🔑 Change Master Password</h3>
            <p>Change your master password. This will re-encrypt all your notes with the new password.</p>
            
            <div class="form-group">
                <label for="current-password">Current Password</label>
                <input type="password" id="current-password" placeholder="Enter current password" required>
            </div>

            <div class="form-group">
                <label for="new-password">New Password</label>
                <input type="password" id="new-password" placeholder="Enter new password" required>
            </div>

            <div class="form-group">
                <label for="confirm-new-password">Confirm New Password</label>
                <input type="password" id="confirm-new-password" placeholder="Confirm new password" required>
            </div>

            <button class="btn" onclick="changePassword()">Change Password</button>
        </div>

        <!-- Two-Factor Authentication Section -->
        <div class="settings-section">
            <h3>🛡️ Two-Factor Authentication</h3>
            <p>Add an extra layer of security to your account with 2FA.</p>
            
            <div id="2fa-status">
                <p>Status: <span id="2fa-status-text">Loading...</span></p>
            </div>

            <!-- 2FA Setup -->
            <div id="2fa-setup" style="display: none;">
                <p>Scan this QR code with your authenticator app:</p>
                <div class="qr-code-container">
                    <div id="qr-code"></div>
                </div>
                <p>Or enter this secret manually: <strong id="2fa-secret"></strong></p>
                
                <div class="form-group">
                    <label for="2fa-verify-code">Enter verification code</label>
                    <input type="text" id="2fa-verify-code" placeholder="000000" maxlength="6" pattern="[0-9]{6}">
                </div>

                <button class="btn" onclick="verify2FASetup()">Enable 2FA</button>
                <button class="btn btn-secondary" onclick="cancel2FASetup()">Cancel</button>
            </div>

            <!-- 2FA Controls -->
            <div id="2fa-controls">
                <button class="btn" id="setup-2fa-btn" onclick="setup2FA()" style="display: none;">Enable 2FA</button>
                <button class="btn btn-danger" id="disable-2fa-btn" onclick="disable2FA()" style="display: none;">Disable 2FA</button>
                <button class="btn btn-secondary" id="generate-backup-codes-btn" onclick="generateBackupCodes()" style="display: none;">Generate Backup Codes</button>
            </div>

            <!-- Backup Codes Display -->
            <div id="backup-codes-display" style="display: none;">
                <div class="backup-codes">
                    <h4>⚠️ Backup Codes - Save These Safely!</h4>
                    <p>These codes can be used to access your account if you lose your authenticator device. Each code can only be used once.</p>
                    <div id="backup-codes-list"></div>
                </div>
                <button class="btn btn-secondary" onclick="hideBackupCodes()">Close</button>
            </div>
        </div>

        <!-- Account Information -->
        <div class="settings-section">
            <h3>👤 Account Information</h3>
            <div id="account-info">
                <p>Email: <span id="user-email">Loading...</span></p>
                <p>Account created: <span id="account-created">Loading...</span></p>
                <p>Last login: <span id="last-login">Loading...</span></p>
            </div>
        </div>
    </div>

    <!-- Loading indicators -->
    <div id="load-settings" class="loading">
        <p>Loading settings...</p>
    </div>

    <div id="load-2fa" class="loading">
        <p>Processing 2FA...</p>
    </div>

    <!-- Scripts -->
    <script src="js/utils.js"></script>
    <script src="js/crypto.js"></script>
    <script src="js/api.js"></script>
    <script src="js/qrcode.min.js"></script>
    <script src="js/settings.js"></script>
    <script>
        // Initialize the settings page
        document.addEventListener('DOMContentLoaded', function() {
            verifyConfigIntegrity();
            initializeActivityTracking();
            initializeSettingsPage();
        });

        // Check authentication on page load
        function checkAuth() {
            if (!currentUser || !encryptionKey || !apiManager || !apiManager.isTokenValid()) {
                window.location.href = 'index.html';
                return false;
            }
            return true;
        }

        // Initialize settings page
        function initializeSettingsPage() {
            if (!checkAuth()) return;
            
            load2FAStatus();
            loadAccountInfo();
            checkAdminStatus();
        }

        // Check admin status and show admin nav if needed
        async function checkAdminStatus() {
            try {
                const result = await apiManager.checkAdminStatus();
                if (result.isAdmin) {
                    document.getElementById('admin-nav').style.display = 'block';
                }
            } catch (error) {
                // Not admin or error checking
            }
        }
    </script>
</body>
</html>
