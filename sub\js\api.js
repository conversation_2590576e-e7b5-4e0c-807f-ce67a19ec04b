// API Manager for SecureNotes - Extracted from index_old.html

// Security: Enhanced API Manager with validation
class APIManager {
    constructor() {
        this.apiBase = CONFIG.API_BASE;
        this.token = this.getSecureToken();
    }

    // Security: Memory-only token storage (enhanced security)
    getSecureToken() {
        // Always return null - no persistent token storage for enhanced security
        return null;
    }

    setSecureToken(token) {
        // Store only in memory for current session
        this.token = token;
        this.tokenExpiry = Date.now() + CONFIG.SESSION_TIMEOUT;
    }

    clearToken() {
        // Clear memory-only token
        this.token = null;
        this.tokenExpiry = null;
    }

    isTokenValid() {
        return this.token && this.tokenExpiry && Date.now() < this.tokenExpiry;
    }

    async request(endpoint, options = {}) {
        return await secureApiRequest(endpoint, {
            ...options,
            headers: {
                ...options.headers,
                ...(this.token && { 'Authorization': `Bearer ${this.token}` })
            }
        });
    }

    async getSalt(email) {
        if (!validateInput(email, 'email')) {
            throw new Error('Invalid email format');
        }

        const result = await this.request(`/api/salt?email=${encodeURIComponent(email)}`);
        if (result && result.salt) {
            return {
                success: true,
                salt: result.salt
            };
        }
        throw new Error('Unable to get user salt, user may not exist.');
    }

    async register(email, authKey, salt) {
        if (!validateInput(email, 'email')) {
            throw new Error('Invalid email format');
        }
        if (!authKey || typeof authKey !== 'string') {
            throw new Error('Invalid auth key');
        }

        return await this.request('/api/register', {
            method: 'POST',
            body: JSON.stringify({
                email,
                authKey,
                salt: Array.from(salt)
            })
        });
    }

    async login(email, authKey) {
        if (!validateInput(email, 'email')) {
            throw new Error('Invalid email format');
        }
        if (!authKey || typeof authKey !== 'string') {
            throw new Error('Invalid auth key');
        }

        const result = await this.request('/api/login', {
            method: 'POST',
            body: JSON.stringify({
                email,
                authKey
            })
        });

        if (result.success) {
            this.setSecureToken(result.token);
        }

        return result;
    }

    async saveNote(encryptedContent, encryptedPreviewTitle) {
        if (!encryptedContent || typeof encryptedContent !== 'string') {
            throw new Error('Invalid encrypted content');
        }

        return await this.request('/api/notes', {
            method: 'POST',
            body: JSON.stringify({
                encryptedContent,
                encryptedPreviewTitle
            })
        });
    }

    // Admin API methods
    async checkAdminStatus() {
        return await this.request('/api/admin/check', {
            method: 'GET'
        });
    }

    async getUsers() {
        return await this.request('/api/admin/users', {
            method: 'GET'
        });
    }

    async toggleUserStatus(userId, disabled) {
        return await this.request(`/api/admin/users/${userId}/toggle`, {
            method: 'PUT',
            body: JSON.stringify({ disabled })
        });
    }

    async getBackupStatus() {
        return await this.request('/api/admin/backup/status', {
            method: 'GET'
        });
    }

    async triggerBackup() {
        return await this.request('/api/admin/backup', {
            method: 'POST'
        });
    }

    async getBackupFilesList() {
        return await this.request('/api/admin/backup/list', {
            method: 'GET'
        });
    }

    async downloadBackupFile(fileId) {
        return await this.request(`/api/admin/backup/download/${fileId}`, {
            method: 'GET'
        });
    }

    async restoreFromBackup(fileId, confirmation) {
        return await this.request('/api/admin/backup/restore', {
            method: 'POST',
            body: JSON.stringify({ fileId, confirmation })
        });
    }

    async updateNote(noteId, encryptedContent, encryptedPreviewTitle) {
        if (!validateInput(noteId, 'noteId')) {
            throw new Error('Invalid note ID');
        }
        if (!encryptedContent || typeof encryptedContent !== 'string') {
            throw new Error('Invalid encrypted content');
        }

        return await this.request(`/api/notes/${noteId}`, {
            method: 'PUT',
            body: JSON.stringify({
                encryptedContent,
                encryptedPreviewTitle
            })
        });
    }

    async deleteNote(noteId) {
        if (!validateInput(noteId, 'noteId')) {
            throw new Error('Invalid note ID');
        }

        return await this.request(`/api/notes/${noteId}`, {
            method: 'DELETE'
        });
    }

    async getNotes() {
        const result = await this.request('/api/notes');
        return result.notes || [];
    }

    async clearAllNotes() {
        return await this.request('/api/notes/clear', {
            method: 'DELETE',
            body: JSON.stringify({
                confirmation: 'DELETE_ALL_NOTES'  // Required confirmation for security
            })
        });
    }

    async changePassword(currentAuthKey, newAuthKey, reencryptedNotes) {
        if (!currentAuthKey || !newAuthKey || !Array.isArray(reencryptedNotes)) {
            throw new Error('Invalid parameters for password change');
        }

        return await this.request('/api/change-password', {
            method: 'POST',
            body: JSON.stringify({
                currentAuthKey,
                newAuthKey,
                reencryptedNotes
            })
        });
    }

    // 2FA API methods
    async setup2FA() {
        return await this.request('/api/2fa/setup', {
            method: 'POST'
        });
    }

    async verify2FA(token) {
        return await this.request('/api/2fa/verify', {
            method: 'POST',
            body: JSON.stringify({ token })
        });
    }

    async disable2FA(token) {
        return await this.request('/api/2fa/disable', {
            method: 'POST',
            body: JSON.stringify({ token })
        });
    }

    async generate2FABackupCodes(token) {
        return await this.request('/api/2fa/backup', {
            method: 'POST',
            body: JSON.stringify({ token })
        });
    }

    async get2FAStatus() {
        return await this.request('/api/2fa/status', {
            method: 'GET'
        });
    }

    async verify2FALogin(token, backupCode) {
        return await this.request('/api/2fa/login', {
            method: 'POST',
            body: JSON.stringify({ token, backupCode })
        });
    }
}

