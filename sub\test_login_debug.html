<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Login Debug Test</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .debug-section { margin: 20px 0; padding: 15px; border: 1px solid #ccc; }
        .error { color: red; }
        .success { color: green; }
        .info { color: blue; }
        button { margin: 5px; padding: 10px; }
        input { margin: 5px; padding: 8px; width: 200px; }
    </style>
</head>
<body>
    <h1>Login Debug Test</h1>
    
    <div class="debug-section">
        <h3>Step 1: Check Access Requirement</h3>
        <button onclick="testCheckAccessRequirement()">Test Check Access Requirement</button>
        <div id="access-debug"></div>
    </div>

    <div class="debug-section">
        <h3>Step 2: Test Login Flow</h3>
        <input type="email" id="test-email" placeholder="Email" value="<EMAIL>">
        <input type="password" id="test-password" placeholder="Password" value="testpass123">
        <button onclick="testLoginFlow()">Test Login Flow</button>
        <div id="login-debug"></div>
    </div>

    <div class="debug-section">
        <h3>Debug Information</h3>
        <div id="debug-info"></div>
    </div>

    <!-- Scripts -->
    <script src="js/utils.js"></script>
    <script src="js/crypto.js"></script>
    <script src="js/api.js"></script>
    <script src="js/auth.js"></script>

    <script>
        function log(elementId, message, type = 'info') {
            const element = document.getElementById(elementId);
            const timestamp = new Date().toLocaleTimeString();
            const className = type;
            element.innerHTML += `<div class="${className}">[${timestamp}] ${message}</div>`;
            console.log(`[${timestamp}] ${message}`);
        }

        function updateDebugInfo() {
            const debugInfo = document.getElementById('debug-info');
            debugInfo.innerHTML = `
                <h4>Current State:</h4>
                <p><strong>window.accessToken:</strong> ${window.accessToken ? 'SET (' + window.accessToken.substring(0, 20) + '...)' : 'NOT SET'}</p>
                <p><strong>window.accessTokenExpiry:</strong> ${window.accessTokenExpiry ? new Date(window.accessTokenExpiry).toLocaleString() : 'NOT SET'}</p>
                <p><strong>window.sessionData:</strong> ${JSON.stringify(window.sessionData, null, 2)}</p>
                <p><strong>window.apiManager:</strong> ${window.apiManager ? 'INITIALIZED' : 'NOT INITIALIZED'}</p>
                <p><strong>window.currentUser:</strong> ${window.currentUser || 'NOT SET'}</p>
            `;
        }

        async function testCheckAccessRequirement() {
            const debugEl = 'access-debug';
            log(debugEl, 'Starting access requirement test...', 'info');
            
            try {
                // Initialize like the main app
                if (typeof updateGlobalAliases === 'function') {
                    updateGlobalAliases();
                    log(debugEl, 'Global aliases updated', 'success');
                }

                if (typeof initializeAPI === 'function') {
                    initializeAPI();
                    log(debugEl, 'API manager initialized', 'success');
                }

                updateDebugInfo();

                // Test the access requirement check
                if (typeof checkAccessRequirement === 'function') {
                    log(debugEl, 'Calling checkAccessRequirement...', 'info');
                    await checkAccessRequirement();
                    log(debugEl, 'checkAccessRequirement completed', 'success');
                } else {
                    log(debugEl, 'checkAccessRequirement function not found', 'error');
                }

                updateDebugInfo();

            } catch (error) {
                log(debugEl, 'Error: ' + error.message, 'error');
                console.error('Full error:', error);
            }
        }

        async function testLoginFlow() {
            const debugEl = 'login-debug';
            const email = document.getElementById('test-email').value;
            const password = document.getElementById('test-password').value;

            log(debugEl, `Starting login test for ${email}...`, 'info');

            try {
                updateDebugInfo();

                // Test getSalt first (should work without access token now)
                log(debugEl, 'Testing getSalt (should work without access token)...', 'info');
                const saltResponse = await window.apiManager.getSalt(email);
                log(debugEl, 'getSalt successful: ' + JSON.stringify(saltResponse), 'success');

                // Check if we have access token for login
                if (!window.accessToken) {
                    log(debugEl, 'No access token found - login will fail', 'error');
                    log(debugEl, 'Need to run access requirement check first', 'info');
                    return;
                }

                // Test full login
                log(debugEl, 'Testing full login...', 'info');
                if (typeof login === 'function') {
                    // Set the form values
                    let emailInput = document.getElementById('login-email');
                    if (!emailInput) {
                        emailInput = document.createElement('input');
                        emailInput.id = 'login-email';
                        document.body.appendChild(emailInput);
                    }
                    emailInput.value = email;

                    let passwordInput = document.getElementById('login-password');
                    if (!passwordInput) {
                        passwordInput = document.createElement('input');
                        passwordInput.id = 'login-password';
                        document.body.appendChild(passwordInput);
                    }
                    passwordInput.value = password;

                    await login();
                    log(debugEl, 'Login completed', 'success');
                } else {
                    log(debugEl, 'login function not found', 'error');
                }

            } catch (error) {
                log(debugEl, 'Error: ' + error.message, 'error');
                console.error('Full error:', error);
            }
        }

        // Initialize on page load
        document.addEventListener('DOMContentLoaded', function() {
            log('debug-info', 'Page loaded, initializing...', 'info');
            updateDebugInfo();
        });
    </script>
</body>
</html>
