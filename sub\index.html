<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="Content-Security-Policy" content="default-src 'self'; script-src 'self' 'unsafe-inline'; style-src 'self' 'unsafe-inline'; connect-src 'self' https://secure-notes-api.v8x.workers.dev; img-src 'self' data:; font-src 'self'">
    <meta http-equiv="Strict-Transport-Security" content="max-age=31536000; includeSubDomains">
    <meta http-equiv="X-Content-Type-Options" content="nosniff">
    <meta http-equiv="Referrer-Policy" content="strict-origin-when-cross-origin">
    <script src="js/qrcode.min.js"></script>
    <title>SecureNotes</title>
    <link rel="stylesheet" href="styles/common.css">
</head>
<body>
    <!-- Progress bar for loading operations -->
    <div id="progress-bar" class="progress-bar"></div>

    <!-- Loading state -->
    <div id="loading-state" class="access-gate">
        <div class="lock-icon">⏳</div>
        <h1>Loading...</h1>
        <p class="subtitle">Checking access requirements...</p>
    </div>

    <!-- Access password verification layer -->
    <div id="access-gate" class="access-gate" style="display: none;">
        <div class="lock-icon">🔒</div>
        <h1>Access Verification</h1>
        <p class="subtitle">Please enter the site access password to continue</p>

        <div class="form-group">
            <label for="site-password">Access Password</label>
            <div class="password-container">
                <input type="password" id="site-password" placeholder="Enter access password" autocomplete="off">
                <span class="password-toggle" onclick="togglePasswordVisibility('site-password')">👁️</span>
            </div>
        </div>

        <button class="btn" id="verify-access-btn">Verify Access</button>
        <div id="access-error" class="error"></div>
    </div>

    <!-- Main application -->
    <div id="main-app" class="main-app">
        <div class="container">
            <div class="header">
                <h1>🔐 SecureNotes</h1>
                <p>End-to-end encrypted secure notes application</p>
            </div>

            <div class="crypto-info">
                <h3>🛡️ Security Guarantee</h3>
                <p>• All notes use end-to-end encryption, server cannot read content</p>
                <p>• Uses AES-256-GCM encryption algorithm and PBKDF2 key derivation</p>
                <p>• Master password never leaves your device</p>
                <p>• Access password protection prevents unauthorized access</p>
            </div>

            <!-- Authentication area -->
            <div id="auth-section" class="card">
                <div class="auth-section">
                    <div class="auth-form" id="login-form">
                        <h3>Login</h3>
                        <div class="form-group">
                            <label for="login-email">Email</label>
                            <input type="email" id="login-email" placeholder="<EMAIL>" maxlength="254">
                        </div>
                        <div class="form-group">
                            <label for="login-password">Master Password</label>
                            <div class="password-container">
                                <input type="password" id="login-password" placeholder="Enter master password" maxlength="64">
                                <span class="password-toggle" onclick="togglePasswordVisibility('login-password')">👁️</span>
                            </div>
                        </div>
                        <button class="btn" id="login-btn">Login</button>
                        <div class="auth-toggle">
                            <a id="show-register">Don't have an account? Register here</a>
                        </div>
                    </div>

                    <div class="auth-form hidden" id="register-form">
                        <h3>Register</h3>
                        <div class="form-group">
                            <label for="register-email">Email</label>
                            <input type="email" id="register-email" placeholder="<EMAIL>" maxlength="254">
                        </div>
                        <div class="form-group">
                            <label for="register-password">Master Password</label>
                            <div class="password-container">
                                <input type="password" id="register-password" placeholder="Create master password (8-64 characters)" minlength="8" maxlength="64">
                                <span class="password-toggle" onclick="togglePasswordVisibility('register-password')">👁️</span>
                            </div>
                            <div id="password-strength-hint" class="password-hint" style="display: none;">
                                <div><strong>💡 Password Security Recommendations:</strong></div>
                                <div class="password-requirements">
                                    <strong>For optimal security, use a strong password with 12+ characters including:</strong>
                                    <ul id="password-requirements-list">
                                        <li id="req-length" class="requirement-unmet">✗ At least 12 characters (current minimum: 8)</li>
                                        <li id="req-uppercase" class="requirement-unmet">✗ Uppercase letters (A-Z)</li>
                                        <li id="req-lowercase" class="requirement-unmet">✗ Lowercase letters (a-z)</li>
                                        <li id="req-numbers" class="requirement-unmet">✗ Numbers (0-9)</li>
                                        <li id="req-symbols" class="requirement-unmet">✗ Special characters (!@#$%^&*)</li>
                                    </ul>
                                    <div style="margin-top: 8px;">
                                        <strong>⚠️ Weak Password Risks:</strong><br>
                                        • Vulnerable to dictionary attacks and brute force<br>
                                        • May compromise your encrypted notes if breached<br>
                                        • Easier for attackers to guess or crack
                                    </div>
                                </div>
                            </div>
                        </div>
                        <button class="btn" id="register-btn">Register</button>
                        <div class="auth-toggle">
                            <a id="show-login">Already have an account? Login here</a>
                        </div>
                    </div>
                </div>
                <div id="auth-status"></div>
            </div>

            <!-- Notes area -->
            <div id="notes-section" class="notes-section">
                <div class="card">
                    <div class="notes-header">
                        <div>
                            <h3>My Encrypted Notes</h3>
                            <div id="last-login-info" style="font-size: 0.9rem; color: #666; margin-top: 5px;"></div>
                        </div>
                        <div class="header-buttons">
                            <button class="btn btn-admin btn-icon" id="admin-btn" style="display: none;" title="Admin">🔧</button>
                            <button class="btn btn-admin btn-icon" id="settings-btn" title="Settings">⚙️</button>
                            <button class="btn btn-admin btn-icon" id="logout-btn" title="Logout">🚪</button>
                        </div>
                    </div>

                    <div id="notes-status"></div>

                    <div class="form-group">
                        <label for="note-content">Write Note</label>
                        <textarea id="note-content" placeholder="Write your thoughts here..." maxlength="1000"></textarea>
                        <div class="char-counter" id="note-char-counter">0/1000</div>
                    </div>

                    <div class="note-controls">
                        <button class="btn" id="save-note-btn">Save Note</button>
                        <button class="btn btn-danger" id="clear-all-notes-btn">Clear All Notes</button>
                        <!-- Search controls -->
                        <div class="search-section">
                            <div class="search-controls">
                                <select id="search-scope" class="search-scope-select" title="Search scope">
                                    <option value="title">Title only (Fast)</option>
                                    <option value="full">Full content (Slower)</option>
                                </select>
                                <div class="search-container">
                                    <input type="text" id="note-search" placeholder="Search notes..." />
                                    <button class="search-clear-btn" id="search-clear-btn" title="Clear search">×</button>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div id="notes-list" style="margin-top: 30px;"></div>

                    <!-- Pagination controls -->
                    <div id="pagination-controls" style="text-align: center; margin-top: 20px; display: none;">
                        <button class="btn btn-secondary" id="prev-page-btn" disabled>Previous</button>
                        <span id="page-info" style="margin: 0 15px;">Page 1 of 1</span>
                        <button class="btn btn-secondary" id="next-page-btn" disabled>Next</button>
                    </div>
                </div>
            </div>

            <!-- Admin section -->
            <div id="admin-section" class="admin-section">
                <div class="card">
                    <div class="admin-header">
                        <h3>🔧 System Administration</h3>
                        <button class="btn btn-secondary" id="back-to-notes-btn">← Back to Notes</button>
                    </div>

                    <!-- Backup Management -->
                    <div class="admin-panel">
                        <h4>📦 Backup Management</h4>
                        <div class="backup-controls">
                            <button class="btn btn-secondary" id="backup-status-btn">View Backup Status</button>
                            <button class="btn btn-primary" id="manual-backup-btn">Manual Backup</button>
                            <button class="btn btn-warning" id="restore-backup-btn">Restore Backup</button>
                        </div>
                        <div id="backup-status-display" class="status-display"></div>
                        <div id="restore-backup-modal" class="modal" style="display: none;">
                            <div class="modal-content">
                                <div class="modal-header">
                                    <h3>🔄 Restore Database from Backup</h3>
                                    <span class="close" id="restore-modal-close">&times;</span>
                                </div>
                                <div class="modal-body">
                                    <div class="warning-box">
                                        <strong>⚠️ Warning:</strong> This operation will completely replace all current data with the backup data. This action cannot be undone!
                                    </div>
                                    <div id="backup-files-list" class="backup-files-container">
                                        <p>Loading backup files...</p>
                                    </div>
                                </div>
                                <div class="modal-footer">
                                    <button class="btn btn-secondary" id="restore-cancel-btn">Cancel</button>
                                    <button class="btn btn-danger" id="restore-confirm-btn" disabled>Restore Selected Backup</button>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- User Management -->
                    <div class="admin-panel">
                        <h4>👥 User Management</h4>
                        <div class="user-controls">
                            <button class="btn btn-secondary" id="refresh-users-btn">Refresh User List</button>
                        </div>
                        <div id="users-list" class="users-list"></div>
                    </div>
                </div>
            </div>

            <!-- Settings area -->
            <div id="settings-section" class="settings-section" style="display: none;">
                <div class="card">
                    <div class="settings-header">
                        <h3>⚙️ Settings</h3>
                        <button class="btn btn-secondary" id="back-to-notes-from-settings-btn">← Back to Notes</button>
                    </div>

                    <div id="settings-status"></div>

                    <!-- Change Password Section -->
                    <div class="settings-group">
                        <h4>🔐 Change Master Password</h4>
                        <p class="settings-description">
                            Changing your master password will re-encrypt all your notes with the new password.
                            <strong>Make sure to remember your new password - it cannot be recovered!</strong>
                        </p>

                        <div class="form-group">
                            <label for="current-password">Current Master Password</label>
                            <div class="password-container">
                                <input type="password" id="current-password" placeholder="Enter current password">
                                <span class="password-toggle" onclick="togglePasswordVisibility('current-password')">👁️</span>
                            </div>
                        </div>

                        <div class="form-group">
                            <label for="new-password">New Master Password</label>
                            <div class="password-container">
                                <input type="password" id="new-password" placeholder="Enter new password (min 8 characters)">
                                <span class="password-toggle" onclick="togglePasswordVisibility('new-password')">👁️</span>
                            </div>
                        </div>

                        <div class="form-group">
                            <label for="confirm-password">Confirm New Password</label>
                            <div class="password-container">
                                <input type="password" id="confirm-password" placeholder="Confirm new password">
                                <span class="password-toggle" onclick="togglePasswordVisibility('confirm-password')">👁️</span>
                            </div>
                        </div>

                        <button class="btn btn-warning" id="change-password-btn">Change Password</button>
                    </div>

                    <!-- Two-Factor Authentication Section -->
                    <div class="settings-group">
                        <h4>🔐 Two-Factor Authentication</h4>
                        <p class="settings-description">
                            Add an extra layer of security to your account with time-based one-time passwords (TOTP).
                            You can use apps like Google Authenticator, Authy, or any compatible TOTP app.
                        </p>

                        <div class="twofa-status-container">
                            <div class="info-item">
                                <label>Status:</label>
                                <span id="2fa-status" class="status-indicator">Checking...</span>
                            </div>
                            <div class="twofa-controls">
                                <button id="2fa-setup-btn" class="btn btn-primary" onclick="show2FASetupModal()" style="display: none;">Enable 2FA</button>
                                <button id="2fa-disable-btn" class="btn btn-warning" onclick="show2FADisableModal()" style="display: none;">Disable 2FA</button>
                                <button id="2fa-backup-btn" class="btn btn-secondary" onclick="show2FABackupModal()" style="display: none;">Backup Codes</button>
                            </div>
                        </div>
                    </div>

                    <!-- Account Information Section -->
                    <div class="settings-group">
                        <h4>👤 Account Information</h4>
                        <div class="info-item">
                            <label>Email:</label>
                            <span id="account-email">-</span>
                        </div>
                        <div class="info-item">
                            <label>Auto-logout timeout:</label>
                            <span>3 minutes of inactivity</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script src="js/utils.js"></script>
    <script src="js/crypto.js"></script>
    <script src="js/api.js"></script>
    <script src="js/auth.js"></script>

    <!-- Application initialization -->
    <script>
        // Initialize application when DOM is loaded
        document.addEventListener('DOMContentLoaded', function() {
            console.log('SecureNotes application starting...');

            try {
                // Security: Verify configuration integrity
                if (typeof verifyConfigIntegrity === 'function') {
                    verifyConfigIntegrity();
                }

                // Update global aliases first
                if (typeof updateGlobalAliases === 'function') {
                    updateGlobalAliases();
                    console.log('Global aliases updated');
                } else {
                    console.warn('updateGlobalAliases function not found');
                }

                // Initialize API manager
                if (typeof initializeAPI === 'function') {
                    initializeAPI();
                    console.log('API manager initialization attempted');
                } else {
                    console.warn('initializeAPI function not found');
                }

                // Add event listeners
                setupEventListeners();
                console.log('Event listeners setup completed');

                // Security: Add activity listeners for auto-logout
                const activityEvents = ['mousedown', 'mousemove', 'keypress', 'scroll', 'touchstart', 'click'];
                activityEvents.forEach(event => {
                    document.addEventListener(event, resetAutoLogoutTimer, true);
                });

                // Check access requirements last
                setTimeout(() => {
                    if (typeof checkAccessRequirement === 'function') {
                        console.log('Starting access requirement check...');
                        checkAccessRequirement();
                    } else {
                        console.error('checkAccessRequirement function not found');
                    }
                }, 100);

            } catch (error) {
                console.error('Error during application initialization:', error);
            }
        });

        // Helper functions for UI management
        function showAuthSection() {
            const authSection = document.getElementById('auth-section');
            if (authSection) {
                authSection.style.display = 'block';
            }
        }

        function showLoginForm() {
            const loginForm = document.getElementById('login-form');
            const registerForm = document.getElementById('register-form');
            if (loginForm && registerForm) {
                loginForm.classList.remove('hidden');
                registerForm.classList.add('hidden');
            }
        }

        function showRegisterForm() {
            const loginForm = document.getElementById('login-form');
            const registerForm = document.getElementById('register-form');
            if (loginForm && registerForm) {
                loginForm.classList.add('hidden');
                registerForm.classList.remove('hidden');
            }
        }

        function togglePasswordVisibility(inputId) {
            const input = document.getElementById(inputId);
            const toggle = input.nextElementSibling;
            if (input.type === 'password') {
                input.type = 'text';
                toggle.textContent = '🙈';
            } else {
                input.type = 'password';
                toggle.textContent = '👁️';
            }
        }

        // Auto-logout timer functions
        function resetAutoLogoutTimer() {
            window.lastActivity = Date.now();
        }

        function setupEventListeners() {
            // Site access form
            const sitePasswordInput = document.getElementById('site-password');
            const verifyAccessBtn = document.getElementById('verify-access-btn');

            if (sitePasswordInput && verifyAccessBtn) {
                sitePasswordInput.addEventListener('keypress', function(e) {
                    if (e.key === 'Enter') {
                        if (typeof verifySiteAccess === 'function') {
                            verifySiteAccess();
                        }
                    }
                });

                verifyAccessBtn.addEventListener('click', function() {
                    if (typeof verifySiteAccess === 'function') {
                        verifySiteAccess();
                    }
                });
            }

            // Auth form toggles
            const showRegisterLink = document.getElementById('show-register');
            const showLoginLink = document.getElementById('show-login');

            if (showRegisterLink) {
                showRegisterLink.addEventListener('click', function(e) {
                    e.preventDefault();
                    if (typeof showRegisterForm === 'function') {
                        showRegisterForm();
                    }
                });
            }

            if (showLoginLink) {
                showLoginLink.addEventListener('click', function(e) {
                    e.preventDefault();
                    if (typeof showLoginForm === 'function') {
                        showLoginForm();
                    }
                });
            }

            // Login and register buttons
            const loginBtn = document.getElementById('login-btn');
            const registerBtn = document.getElementById('register-btn');

            if (loginBtn) {
                loginBtn.addEventListener('click', function() {
                    if (typeof login === 'function') {
                        login();
                    }
                });
            }

            if (registerBtn) {
                registerBtn.addEventListener('click', function() {
                    if (typeof register === 'function') {
                        register();
                    }
                });
            }

            // Password strength indicator for register password
            const registerPasswordInput = document.getElementById('register-password');
            if (registerPasswordInput) {
                registerPasswordInput.addEventListener('input', function(e) {
                    const hintElement = document.getElementById('password-strength-hint');
                    if (e.target.value.length > 0) {
                        hintElement.style.display = 'block';
                        if (typeof updatePasswordStrengthIndicator === 'function') {
                            updatePasswordStrengthIndicator(e.target.value);
                        }
                    } else {
                        hintElement.style.display = 'none';
                    }
                });

                // Show password hint when register password field is focused
                registerPasswordInput.addEventListener('focus', function(e) {
                    const hintElement = document.getElementById('password-strength-hint');
                    if (e.target.value.length > 0) {
                        hintElement.style.display = 'block';
                        if (typeof updatePasswordStrengthIndicator === 'function') {
                            updatePasswordStrengthIndicator(e.target.value);
                        }
                    }
                });
            }

            // Enter key handlers for login and register forms
            const loginEmailInput = document.getElementById('login-email');
            const loginPasswordInput = document.getElementById('login-password');
            const registerEmailInput = document.getElementById('register-email');

            if (loginEmailInput) {
                loginEmailInput.addEventListener('keypress', function(e) {
                    if (e.key === 'Enter') {
                        e.preventDefault();
                        if (typeof login === 'function') {
                            login();
                        }
                    }
                });
            }

            if (loginPasswordInput) {
                loginPasswordInput.addEventListener('keypress', function(e) {
                    if (e.key === 'Enter') {
                        e.preventDefault();
                        if (typeof login === 'function') {
                            login();
                        }
                    }
                });
            }

            if (registerEmailInput) {
                registerEmailInput.addEventListener('keypress', function(e) {
                    if (e.key === 'Enter') {
                        e.preventDefault();
                        if (typeof register === 'function') {
                            register();
                        }
                    }
                });
            }

            if (registerPasswordInput) {
                registerPasswordInput.addEventListener('keypress', function(e) {
                    if (e.key === 'Enter') {
                        e.preventDefault();
                        if (typeof register === 'function') {
                            register();
                        }
                    }
                });
            }


        }
    </script>
</body>
</html>
