<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="Content-Security-Policy" content="default-src 'self'; script-src 'self' 'unsafe-inline'; style-src 'self' 'unsafe-inline'; connect-src 'self' https://secure-notes-api.v8x.workers.dev; img-src 'self' data:; font-src 'self'">
    <meta http-equiv="Strict-Transport-Security" content="max-age=31536000; includeSubDomains">
    <meta http-equiv="X-Content-Type-Options" content="nosniff">
    <meta http-equiv="Referrer-Policy" content="strict-origin-when-cross-origin">
    <title>SecureNotes - Admin Panel</title>
    <link rel="stylesheet" href="styles/common.css">
    <link rel="stylesheet" href="styles/admin.css">
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔐 SecureNotes</h1>
            <p>Administrator Panel</p>
        </div>

        <!-- Navigation -->
        <div class="nav-tabs">
            <a href="notes.html" class="nav-tab">My Notes</a>
            <a href="settings.html" class="nav-tab">Settings</a>
            <button class="nav-tab active">Admin</button>
            <button class="nav-tab" onclick="logout()">Logout</button>
        </div>

        <!-- Admin Status -->
        <div id="admin-status" class="status"></div>

        <!-- System Statistics -->
        <div class="admin-section">
            <h3>📊 System Statistics</h3>
            <div id="system-stats" class="stats-grid">
                <div class="stat-card">
                    <h4>Total Users</h4>
                    <div class="stat-value" id="total-users">Loading...</div>
                </div>
                <div class="stat-card">
                    <h4>Total Notes</h4>
                    <div class="stat-value" id="total-notes">Loading...</div>
                </div>
                <div class="stat-card">
                    <h4>Active Sessions</h4>
                    <div class="stat-value" id="active-sessions">Loading...</div>
                </div>
                <div class="stat-card">
                    <h4>System Health</h4>
                    <div class="stat-value" id="system-health">Loading...</div>
                </div>
            </div>
        </div>

        <!-- User Management -->
        <div class="admin-section">
            <h3>👥 User Management</h3>
            
            <div class="admin-controls">
                <button class="btn" onclick="loadUsers()">Refresh Users</button>
                <button class="btn btn-secondary" onclick="exportUserData()">Export Data</button>
            </div>

            <div class="search-section">
                <input type="text" id="user-search" placeholder="Search users by email..." />
            </div>

            <div id="users-list" class="users-list">
                <p>Loading users...</p>
            </div>
        </div>

        <!-- System Logs -->
        <div class="admin-section">
            <h3>📋 System Logs</h3>
            
            <div class="admin-controls">
                <select id="log-level">
                    <option value="all">All Levels</option>
                    <option value="error">Errors Only</option>
                    <option value="warning">Warnings</option>
                    <option value="info">Info</option>
                </select>
                <button class="btn" onclick="loadLogs()">Refresh Logs</button>
                <button class="btn btn-secondary" onclick="clearLogs()">Clear Logs</button>
            </div>

            <div id="logs-container" class="logs-container">
                <p>Loading logs...</p>
            </div>
        </div>

        <!-- System Configuration -->
        <div class="admin-section">
            <h3>⚙️ System Configuration</h3>
            
            <div class="config-grid">
                <div class="config-item">
                    <label for="max-users">Max Users</label>
                    <input type="number" id="max-users" min="1" max="10000">
                </div>
                <div class="config-item">
                    <label for="session-timeout">Session Timeout (minutes)</label>
                    <input type="number" id="session-timeout" min="5" max="1440">
                </div>
                <div class="config-item">
                    <label for="max-notes-per-user">Max Notes per User</label>
                    <input type="number" id="max-notes-per-user" min="1" max="10000">
                </div>
                <div class="config-item">
                    <label for="maintenance-mode">Maintenance Mode</label>
                    <select id="maintenance-mode">
                        <option value="false">Disabled</option>
                        <option value="true">Enabled</option>
                    </select>
                </div>
            </div>

            <button class="btn" onclick="saveConfiguration()">Save Configuration</button>
        </div>

        <!-- Backup & Maintenance -->
        <div class="admin-section">
            <h3>💾 Backup & Maintenance</h3>
            
            <div class="admin-controls">
                <button class="btn" onclick="createBackup()">Create Backup</button>
                <button class="btn btn-secondary" onclick="downloadBackup()">Download Latest Backup</button>
                <button class="btn btn-danger" onclick="performMaintenance()">Run Maintenance</button>
            </div>

            <div id="backup-status" class="backup-status">
                <p>Last backup: <span id="last-backup">Loading...</span></p>
                <p>Backup size: <span id="backup-size">Loading...</span></p>
            </div>
        </div>
    </div>

    <!-- Loading indicators -->
    <div id="load-admin" class="loading">
        <p>Loading admin panel...</p>
    </div>

    <div id="load-users" class="loading">
        <p>Loading users...</p>
    </div>

    <div id="load-logs" class="loading">
        <p>Loading logs...</p>
    </div>

    <!-- Scripts -->
    <script src="js/utils.js"></script>
    <script src="js/crypto.js"></script>
    <script src="js/api.js"></script>
    <script src="js/admin.js"></script>
    <script>
        // Initialize the admin page
        document.addEventListener('DOMContentLoaded', function() {
            verifyConfigIntegrity();
            initializeActivityTracking();
            initializeAdminPage();
        });

        // Check authentication and admin privileges
        function checkAuth() {
            if (!currentUser || !encryptionKey || !apiManager || !apiManager.isTokenValid()) {
                window.location.href = 'index.html';
                return false;
            }
            return true;
        }

        // Initialize admin page
        async function initializeAdminPage() {
            if (!checkAuth()) return;
            
            try {
                // Check admin status
                const result = await apiManager.checkAdminStatus();
                if (!result.isAdmin) {
                    showStatus('admin-status', 'Access denied: Admin privileges required', 'error');
                    setTimeout(() => {
                        window.location.href = 'notes.html';
                    }, 2000);
                    return;
                }

                // Load admin data
                loadSystemStats();
                loadUsers();
                loadLogs();
                loadConfiguration();
                loadBackupStatus();
                
                // Set up event listeners
                setupAdminEventListeners();
                
            } catch (error) {
                showStatus('admin-status', 'Error loading admin panel: ' + error.message, 'error');
            }
        }

        function setupAdminEventListeners() {
            // User search
            document.getElementById('user-search').addEventListener('input', function(e) {
                const keyword = e.target.value.trim().toLowerCase();
                filterUsers(keyword);
            });

            // Log level filter
            document.getElementById('log-level').addEventListener('change', function() {
                loadLogs();
            });
        }

        function filterUsers(keyword) {
            const userItems = document.querySelectorAll('.user-item');
            userItems.forEach(item => {
                const email = item.querySelector('.user-email').textContent.toLowerCase();
                if (email.includes(keyword)) {
                    item.style.display = 'block';
                } else {
                    item.style.display = 'none';
                }
            });
        }
    </script>
</body>
</html>
