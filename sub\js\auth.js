// Authentication module for SecureNotes - Extracted from index_old.html

/**
 * Security: Check if site access password is required
 */
async function checkAccessRequirement() {
    // Show loading state
    LoadingManager.showLoading('check-access');

    try {
        const response = await secureApiRequest('/api/check-access-requirement');

        // Hide loading state
        document.getElementById('loading-state').style.display = 'none';

        if (response.success) {
            if (!response.siteAccessRequired) {
                // Site access not required, use auto-generated token
                window.accessToken = response.accessToken;
                window.accessTokenExpiry = Date.now() + (2 * 60 * 60 * 1000); // 2 hours

                // Update session data
                window.sessionData.siteAccessVerified = true;
                window.sessionData.accessTimestamp = Date.now();
                window.sessionData.accessExpiry = Date.now() + (2 * 60 * 60 * 1000); // 2 hours

                // Check if user is already logged in
                if (window.currentUser && window.apiManager && window.apiManager.isTokenValid()) {
                    // User is logged in, redirect to notes page directly
                    window.location.href = 'notes.html';
                } else {
                    // No user logged in, show login page
                    document.getElementById('main-app').classList.add('show');
                    showAuthSection();
                }
                return;
            } else {
                // Site access required, show access gate
                document.getElementById('access-gate').style.display = 'block';
                // Check existing session
                checkSiteAccess();
            }
        } else {
            // Fallback to requiring access password
            document.getElementById('access-gate').style.display = 'block';
            checkSiteAccess();
        }
    } catch (error) {
        console.error('Error checking access requirement:', error);
        // Hide loading state and fallback to requiring access password
        document.getElementById('loading-state').style.display = 'none';
        document.getElementById('access-gate').style.display = 'block';
        checkSiteAccess();
    } finally {
        // Hide loading state
        LoadingManager.hideLoading('check-access');
    }
}

/**
 * Security: Memory-only access verification check (enhanced security)
 */
function checkSiteAccess() {
    try {
        // Check memory-based session data only (no persistent storage)
        if (window.sessionData.siteAccessVerified &&
            window.sessionData.accessExpiry &&
            Date.now() < window.sessionData.accessExpiry &&
            window.accessToken &&
            window.accessTokenExpiry &&
            Date.now() < window.accessTokenExpiry) {

            document.getElementById('access-gate').style.display = 'none';
            document.getElementById('main-app').classList.add('show');

            // Check if user is already logged in and show appropriate page
            if (window.currentUser && window.apiManager && window.apiManager.isTokenValid()) {
                // User is logged in, redirect to notes page directly
                window.location.href = 'notes.html';
            } else {
                // No user logged in, show login page
                showAuthSection();
            }

            // Start auto logout timer
            startAutoLogoutTimer();
        } else {
            // Session expired or invalid, clear data and show access gate
            clearSessionData();
            document.getElementById('access-gate').style.display = 'block';
        }
    } catch (error) {
        console.error('Site access check error:', error);
        // Fallback to showing access gate
        document.getElementById('access-gate').style.display = 'block';
    }
}

/**
 * Security: Clear all session data (privacy mode compatible)
 */
function clearSessionData() {
    // Clear memory-only session data
    window.sessionData.siteAccessVerified = false;
    window.sessionData.accessTimestamp = null;
    window.sessionData.accessExpiry = null;
    window.sessionData.lastActivity = Date.now();

    // Clear access tokens
    window.accessToken = null;
    window.accessTokenExpiry = null;
}

async function verifySiteAccess() {
    const password = document.getElementById('site-password').value;
    const errorEl = document.getElementById('access-error');

    if (!validateInput(password, 'password')) {
        errorEl.textContent = 'Please enter a valid access password';
        return;
    }

    // Show loading state
    LoadingManager.showButtonLoading('verify-access-btn');
    errorEl.textContent = '';

    try {
        // Calculate hash of input password
        const hashedInput = await sha256(password);

        // Get access hash from server for verification
        const result = await secureApiRequest('/api/verify-access', {
            method: 'POST',
            body: JSON.stringify({ hash: hashedInput })
        });

        if (result.success && result.accessToken) {
            // Security: Store access token in memory (privacy mode compatible)
            window.accessToken = result.accessToken;
            window.accessTokenExpiry = Date.now() + (2 * 60 * 60 * 1000); // 2 hours

            // Clear password input for security
            document.getElementById('site-password').value = '';

            document.getElementById('access-gate').style.display = 'none';
            document.getElementById('main-app').classList.add('show');

            // Security: Update session data in memory
            window.sessionData.siteAccessVerified = true;
            window.sessionData.accessTimestamp = Date.now();
            window.sessionData.accessExpiry = Date.now() + (2 * 60 * 60 * 1000); // 2 hours

            // Start auto logout timer
            startAutoLogoutTimer();
        } else {
            errorEl.textContent = 'Invalid access password, please try again';
        }
    } catch (error) {
        console.error('Verification failed:', error);
        errorEl.textContent = 'Verification failed, please check network connection';
    } finally {
        // Hide loading state
        LoadingManager.hideButtonLoading('verify-access-btn');
    }
}

/**
 * Security: Enhanced auto logout functionality
 */
function startAutoLogoutTimer() {
    function checkInactivity() {
        if (Date.now() - window.lastActivity > CONFIG.AUTO_LOGOUT_INACTIVE) {
            logout();
            alert('You have been automatically logged out due to inactivity');
        }
    }

    window.inactivityTimer = setInterval(checkInactivity, 60000); // Check every minute

    // Listen for user activity
    ['mousedown', 'mousemove', 'keypress', 'scroll', 'touchstart'].forEach(event => {
        document.addEventListener(event, () => {
            window.lastActivity = Date.now();
        }, { passive: true });
    });
}

function showLoginForm() {
    document.getElementById('login-form').classList.remove('hidden');
    document.getElementById('register-form').classList.add('hidden');
}

function showRegisterForm() {
    document.getElementById('register-form').classList.remove('hidden');
    document.getElementById('login-form').classList.add('hidden');
}

async function register() {
    const email = document.getElementById('register-email').value.trim();
    const password = document.getElementById('register-password').value;

    if (!validateInput(email, 'email') || !validateInput(password, 'password')) {
        showStatus('auth-status', 'Please fill in all fields correctly', 'error');
        return;
    }

    if (password.length < 8) {
        showStatus('auth-status', 'Master password must be at least 8 characters', 'error');
        return;
    }

    // Show loading state
    LoadingManager.showButtonLoading('register-btn');
    showStatus('auth-status', 'Creating account...', 'info');

    try {
        const baseSalt = CryptoUtils.generateSalt();
        const authSalt = await CryptoUtils.deriveSubSalt(baseSalt, 'auth');
        const authKey = await CryptoUtils.deriveAuthKey(password, authSalt);
        const result = await window.apiManager.register(email, authKey, baseSalt);

        if (result.success) {
            showStatus('auth-status', 'Registration successful! Switching to login...', 'success');
            // Clear form for security
            document.getElementById('register-email').value = '';
            document.getElementById('register-password').value = '';

            // Auto switch to login form after 1.5 seconds
            setTimeout(() => {
                showLoginForm();
                showStatus('auth-status', 'Please login with your new account', 'info');
            }, 1500);
        } else {
            // Show error message for failed registration
            showStatus('auth-status', result.error || 'Registration failed, please try again', 'error');
        }
    } catch (error) {
        console.error('Registration error:', error);
        showStatus('auth-status', error.message || 'Registration failed, please try again', 'error');
    } finally {
        // Hide loading state
        LoadingManager.hideButtonLoading('register-btn');
    }
}

async function login() {
    const email = document.getElementById('login-email').value.trim();
    const password = document.getElementById('login-password').value;

    if (!validateInput(email, 'email') || !validateInput(password, 'password')) {
        showStatus('auth-status', 'Please fill in all fields correctly', 'error');
        return;
    }

    // Show loading state
    LoadingManager.showButtonLoading('login-btn');
    showStatus('auth-status', 'Authenticating...', 'info');

    // Security: Check if account is locked
    if (isAccountLocked(email)) {
        showStatus('auth-status', 'Account temporarily locked due to too many failed attempts. Try again later.', 'error');
        LoadingManager.hideButtonLoading('login-btn');
        return;
    }

    try {
        const saltResponse = await window.apiManager.getSalt(email);
        const baseSalt = new Uint8Array(saltResponse.salt);
        const authSalt = await CryptoUtils.deriveSubSalt(baseSalt, 'auth');
        const authKey = await CryptoUtils.deriveAuthKey(password, authSalt);
        const result = await window.apiManager.login(email, authKey);

        if (result.success) {
            // Clear failed attempts on successful login
            clearLoginAttempts(email);

            // Set JWT token for API requests
            window.apiManager.setSecureToken(result.token);

            const encryptionSalt = await CryptoUtils.deriveSubSalt(baseSalt, 'encryption');
            window.encryptionKey = await CryptoUtils.deriveKey(password, encryptionSalt);
            window.currentUser = email;
            window.lastLoginAt = result.lastLoginAt;

            // Current user email is already stored in memory variable

            showStatus('auth-status', 'Login successful! Redirecting to notes...', 'success');

            // Clear password for security
            document.getElementById('login-password').value = '';

            setTimeout(() => {
                // Redirect to notes page
                window.location.href = 'notes.html';
            }, 1000);
        } else if (result.requires2FA) {
            // 2FA required - show 2FA verification modal
            LoadingManager.hideButtonLoading('login-btn');
            if (typeof show2FALoginModal === 'function') {
                show2FALoginModal(result.tempToken, email, password, baseSalt);
            } else {
                showStatus('auth-status', '2FA verification required but modal not available', 'error');
            }
            return;
        } else {
            // Record failed login attempt
            recordFailedLogin(email);

            // Show error message for failed login
            showStatus('auth-status', result.error || 'Invalid email or password, please try again', 'error');
        }
    } catch (error) {
        console.error('Login error:', error);

        // Check if this is a site access verification error
        if (error.message && error.message.includes('Site access verification required')) {
            // Clear session data and re-check access requirements
            clearSessionData();

            // Hide main app and show loading state
            const mainApp = document.getElementById('main-app');
            const loadingState = document.getElementById('loading-state');
            if (mainApp) mainApp.classList.remove('show');
            if (loadingState) loadingState.style.display = 'block';

            // Re-check access requirements
            setTimeout(() => {
                if (typeof checkAccessRequirement === 'function') {
                    checkAccessRequirement();
                }
            }, 100);

            showStatus('auth-status', 'Site access verification required. Please verify access first.', 'error');
        } else {
            recordFailedLogin(email);
            showStatus('auth-status', error.message || 'Login failed, please try again', 'error');
        }
    } finally {
        // Hide loading state
        LoadingManager.hideButtonLoading('login-btn');
    }
}

function logout() {
    // Clear all user data
    window.currentUser = null;
    window.encryptionKey = null;
    window.notes = [];
    window.allNotes = [];
    window.decryptedNotes.clear();
    window.editingNoteId = null;
    window.lastLoginAt = null;
    window.isAdmin = false;

    // Clear API manager token
    if (window.apiManager) {
        window.apiManager.clearToken();
    }

    // Clear auto logout timer
    if (window.autoLogoutTimer) {
        clearInterval(window.autoLogoutTimer);
        window.autoLogoutTimer = null;
    }

    // Clear inactivity timer
    if (window.inactivityTimer) {
        clearInterval(window.inactivityTimer);
        window.inactivityTimer = null;
    }

    // Clear session data
    clearSessionData();

    // Hide admin button
    hideAdminButton();

    // Show auth section
    showAuthSection();

    // Clear any status messages
    showStatus('auth-status', '', '');
    showStatus('notes-status', '', '');

    // Clear form fields for security
    document.getElementById('login-email').value = '';
    document.getElementById('login-password').value = '';
    document.getElementById('register-email').value = '';
    document.getElementById('register-password').value = '';

    // Show login form by default
    showLoginForm();
}

// Initialize API manager when auth module loads
function initializeAPI() {
    if (typeof APIManager !== 'undefined' && !window.apiManager) {
        window.apiManager = new APIManager();
        console.log('API Manager initialized');
    }
}
