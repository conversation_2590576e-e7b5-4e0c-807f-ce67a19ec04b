// Cryptographic utilities for SecureNotes - Extracted from index_old.html

// Encryption utility functions
class CryptoUtils {
    static async deriveKey(password, salt) {
        const enc = new TextEncoder();
        const keyMaterial = await window.crypto.subtle.importKey(
            'raw',
            enc.encode(password),
            { name: 'PBKDF2' },
            false,
            ['deriveKey']
        );

        const key = await window.crypto.subtle.deriveKey(
            {
                name: 'PBKDF2',
                salt: salt,
                iterations: 200000, // Increased for better security
                hash: 'SHA-256',
            },
            keyMaterial,
            { name: 'AES-GCM', length: 256 },
            true,
            ['encrypt', 'decrypt']
        );

        return key;
    }

    static async deriveAuthKey(password, salt) {
        const enc = new TextEncoder();
        const keyMaterial = await window.crypto.subtle.importKey(
            'raw',
            enc.encode(password),
            { name: 'PBKDF2' },
            false,
            ['deriveKey']
        );

        const authKey = await window.crypto.subtle.deriveKey(
            {
                name: 'PBKDF2',
                salt: salt,
                iterations: 300000, // Higher iterations for auth key security
                hash: 'SHA-256',
            },
            keyMaterial,
            { name: 'HMAC', hash: 'SHA-256' },
            true,
            ['sign']
        );

        const exported = await window.crypto.subtle.exportKey('raw', authKey);
        return btoa(String.fromCharCode(...new Uint8Array(exported)));
    }

    static async encryptText(plainText, key) {
        try {
            // Validate inputs
            if (!plainText || typeof plainText !== 'string') {
                throw new Error('Invalid plaintext data');
            }
            if (!key) {
                throw new Error('Invalid encryption key');
            }

            // Validate key before use
            if (!(await this.validateKeyStrength(key))) {
                throw new Error('Encryption key failed validation');
            }

            const enc = new TextEncoder();
            const encodedText = enc.encode(plainText);

            // Generate cryptographically secure random IV
            const iv = window.crypto.getRandomValues(new Uint8Array(12));

            const encrypted = await window.crypto.subtle.encrypt(
                { name: 'AES-GCM', iv: iv },
                key,
                encodedText
            );

            const combined = new Uint8Array(iv.length + encrypted.byteLength);
            combined.set(iv);
            combined.set(new Uint8Array(encrypted), iv.length);

            const result = btoa(String.fromCharCode(...combined));

            // Clear sensitive data
            this.clearSensitiveData(encodedText);
            this.clearSensitiveData(iv);

            return result;
        } catch (error) {
            throw new Error('Encryption failed');
        }
    }

    static async decryptText(encryptedData, key) {
        try {
            // Validate inputs
            if (!encryptedData || typeof encryptedData !== 'string') {
                throw new Error('Invalid encrypted data');
            }
            if (!key) {
                throw new Error('Invalid decryption key');
            }

            const combined = new Uint8Array([...atob(encryptedData)].map(c => c.charCodeAt(0)));

            // Validate minimum length (12 bytes IV + at least 1 byte data + 16 bytes auth tag)
            if (combined.length < 29) {
                throw new Error('Invalid encrypted data format');
            }

            const iv = combined.slice(0, 12);
            const encrypted = combined.slice(12);

            const decrypted = await window.crypto.subtle.decrypt(
                { name: 'AES-GCM', iv: iv },
                key,
                encrypted
            );

            const dec = new TextDecoder();
            const result = dec.decode(decrypted);

            // Validate decrypted content is valid UTF-8
            if (result.includes('\uFFFD')) {
                throw new Error('Decrypted content contains invalid characters');
            }

            return result;
        } catch (error) {
            // Security: Don't log specific error details that could leak information
            throw new Error('Decryption failed');
        }
    }

    static generateSalt() {
        return window.crypto.getRandomValues(new Uint8Array(16));
    }

    // Security: Derive different salts for different purposes to avoid cryptographic conflicts
    static async deriveSubSalt(baseSalt, purpose) {
        const enc = new TextEncoder();
        const purposeBytes = enc.encode(purpose);
        const combined = new Uint8Array(baseSalt.length + purposeBytes.length);
        combined.set(baseSalt);
        combined.set(purposeBytes, baseSalt.length);

        const hashBuffer = await crypto.subtle.digest('SHA-256', combined);
        return new Uint8Array(hashBuffer.slice(0, 16)); // Use first 16 bytes as salt
    }

    // Security: Validate salt consistency
    static validateSaltConsistency(salt1, salt2) {
        if (!salt1 || !salt2) return false;
        if (salt1.length !== salt2.length) return false;
        return salt1.every((val, index) => val === salt2[index]);
    }

    // Security: Clear sensitive data from memory
    static clearSensitiveData(data) {
        if (data instanceof Uint8Array) {
            data.fill(0);
        } else if (typeof data === 'string') {
            // Note: In JavaScript, strings are immutable, but we can at least null the reference
            data = null;
        }
    }

    // Security: Validate encryption key strength
    static async validateKeyStrength(key) {
        try {
            // Export key to check its properties
            const exported = await window.crypto.subtle.exportKey('raw', key);
            const keyBytes = new Uint8Array(exported);

            // Check key length (should be 32 bytes for AES-256)
            if (keyBytes.length !== 32) {
                throw new Error('Invalid key length');
            }

            // Basic entropy check (not cryptographically rigorous but better than nothing)
            const uniqueBytes = new Set(keyBytes).size;
            if (uniqueBytes < 16) { // At least 16 different byte values
                throw new Error('Key appears to have low entropy');
            }

            return true;
        } catch (error) {
            console.error('Key validation failed:', error);
            return false;
        }
    }
}


