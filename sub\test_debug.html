<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Debug Test</title>
</head>
<body>
    <h1>Debug Test</h1>
    <div id="debug-output"></div>
    <button onclick="testAccessRequirement()">Test Access Requirement</button>
    <button onclick="testLogin()">Test Login</button>

    <!-- Scripts -->
    <script src="js/utils.js"></script>
    <script src="js/crypto.js"></script>
    <script src="js/api.js"></script>
    <script src="js/auth.js"></script>

    <script>
        function log(message) {
            const output = document.getElementById('debug-output');
            output.innerHTML += '<p>' + new Date().toLocaleTimeString() + ': ' + message + '</p>';
            console.log(message);
        }

        async function testAccessRequirement() {
            log('Testing access requirement...');
            try {
                // Update global aliases first
                if (typeof updateGlobalAliases === 'function') {
                    updateGlobalAliases();
                    log('Global aliases updated');
                } else {
                    log('WARNING: updateGlobalAliases function not found');
                }

                // Initialize API manager
                if (typeof initializeAPI === 'function') {
                    initializeAPI();
                    log('API manager initialized');
                } else {
                    log('WARNING: initializeAPI function not found');
                }

                // Check current state
                log('Current window.accessToken: ' + (window.accessToken ? 'SET' : 'NOT SET'));
                log('Current window.sessionData: ' + JSON.stringify(window.sessionData));

                // Check access requirements
                if (typeof checkAccessRequirement === 'function') {
                    log('Calling checkAccessRequirement...');
                    await checkAccessRequirement();
                    log('Access requirement check completed');
                    log('Access token after check: ' + (window.accessToken ? 'SET' : 'NOT SET'));
                    log('Access token expiry: ' + (window.accessTokenExpiry ? new Date(window.accessTokenExpiry).toLocaleString() : 'NOT SET'));
                    log('Session data after check: ' + JSON.stringify(window.sessionData));
                } else {
                    log('ERROR: checkAccessRequirement function not found');
                }
            } catch (error) {
                log('ERROR: ' + error.message);
                console.error('Full error:', error);
            }
        }

        async function testLogin() {
            log('Testing login...');
            try {
                if (!window.apiManager) {
                    log('ERROR: API manager not initialized');
                    return;
                }

                const email = '<EMAIL>';
                log('Attempting to get salt for: ' + email);
                
                const saltResponse = await window.apiManager.getSalt(email);
                log('Salt response: ' + JSON.stringify(saltResponse));
            } catch (error) {
                log('ERROR: ' + error.message);
            }
        }

        // Initialize on page load
        document.addEventListener('DOMContentLoaded', function() {
            log('Page loaded, starting debug test...');
            testAccessRequirement();
        });
    </script>
</body>
</html>
